"use client";

import { ContactSupport } from "@styled-icons/material-sharp/ContactSupport";
import FsLightbox from "fslightbox-react";
import { InfiniteZoomFader } from "infinite-zoom-fader";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { BookReader } from "styled-icons/boxicons-solid";
import { Quiz } from "styled-icons/material";
import QuizModal from "./QuizModal";
import { BookOpen } from "styled-icons/heroicons-outline";
import { Play } from "styled-icons/ionicons-outline";
import { getSlider } from "@/services/SliderService";
import { getHeroBanner, getHeroSliderText } from "@/services/HeroService";
import i18next from "i18next";

const HeroBanner = () => {
  // To open the lightbox change the value of the "toggler" prop.
  const { t } = useTranslation();
  const [toggler, setToggler] = useState<boolean>(false);
  const [brochureToggler, setBrochureToggler] = useState<boolean>(false);
  const [isQuizModalOpen, setIsQuizModalOpen] = useState<boolean>(false);
  const [heroData, setHeroData] = useState<any>([]);
  const [languageId, setLanguageId] = useState<number>(1);
  const [sliderText, setSliderText] = useState<any>([]);
  const [imagesLoading, setImagesLoading] = useState<boolean>(true);
  const [sliderData, setSliderData] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      setImagesLoading(true);
      try {
        const response = await getSlider();
        if (response.data && response.data.Data !== sliderData) {
         console.log("response.data.Data", response.data.Data)
          setSliderData(response.data.Data); // Only update if data has changed
        }
      } catch (error) {
        console.error("Failed to fetch slider data:", error);
      } finally {
        setImagesLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const handleLanguageChange = () => {
      const languageCode =
        i18next.language || localStorage.getItem("i18nextLng");
      setLanguageId(languageCode === "tr" ? 1 : 2);
    };

    handleLanguageChange();

    i18next.on("languageChanged", handleLanguageChange);

    return () => {
      i18next.off("languageChanged", handleLanguageChange);
    };
  }, []);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await getHeroSliderText(languageId);
        if (response.data) {
          setSliderText(response.data.Data);
        }
      } catch (error) {
        console.error("Failed to fetch FAQs:", error);
      }
    }
    fetchData();
  }, [languageId]);

  useEffect(() => {
    const getHeroData = async () => {
      try {
        const response = await getHeroBanner();
        setHeroData(response.data.Data);
      } catch (error) {
        console.error("Failed to fetch hero data:", error);
      }
    };
    getHeroData();
  }, []);

  const responseImages = {
    desktop: sliderData.map((item) => {
      return {
        src: item.Image,
        alt: item.Id.toString() + " alt",
      };
    }),
    mobile: sliderData.map((item) => ({
      src: item.Image,
      alt: item.Id.toString() + " alt",
    })),
  };

  /*   const images = {
    desktop: [
      {
        src: sliderData[0]?.Image,
        alt: "Image 1 alt",
      },
      {
        src: sliderData[1]?.Image,
        alt: "Image 2 alt",
      },
      {
        src: sliderData[2]?.Image,
        alt: "Image 3 alt",
      },
      {
        src: sliderData[3]?.Image,
        alt: "Image 4 alt",
      },
      {
        src: sliderData[4]?.Image ? sliderData[4]?.Image : sliderData[0]?.Image,
        alt: "Image 5 alt",
      },
      {
        src: sliderData[5]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,
        alt: "Image 5 alt",
      },
      {
        src: sliderData[6]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,
        alt: "Image 5 alt",
      },
      {
        src: sliderData[7]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,
        alt: "Image 5 alt",
      },
      {
        src: sliderData[8]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,
        alt: "Image 5 alt",
      },
    ],
    mobile: [
      {
        src: sliderData[0]?.Image,
        alt: "Image 1 alt",
      },
      {
        src: sliderData[1]?.Image,
        alt: "Image 2 alt",
      },
      {
        src: sliderData[2]?.Image,
        alt: "Image 3 alt",
      },
      {
        src: sliderData[3]?.Image,
        alt: "Image 4 alt",
      },
    ],
  }; */

  // change title and subtitle with the images, in every 5 seconds
  const [title, setTitle] = useState<string>(t("hero.title"));
  const [subtitle, setSubtitle] = useState<string>(t("hero.subtitle"));

  const [index, setIndex] = useState<number>(0);

  useEffect(() => {
    const interval = setInterval(() => {
      switch (index) {
        case 0:
          setTitle(
            sliderText[0]?.Title ? sliderText[0]?.Title : t("hero.title")
          );
          setSubtitle(
            sliderText[0]?.Content ? sliderText[0]?.Content : t("hero.subtitle")
          );
          setIndex(1);
          break;
        case 1:
          setTitle(
            sliderText[1]?.Title ? sliderText[1]?.Title : t("hero.title")
          );
          setSubtitle(
            sliderText[1]?.Content
              ? sliderText[1]?.Content
              : t("hero.subtitle2")
          );
          setIndex(2);
          break;
        case 2:
          setTitle(
            sliderText[2]?.Title ? sliderText[2]?.Title : t("hero.title")
          );
          setSubtitle(
            sliderText[2]?.Content
              ? sliderText[2]?.Content
              : t("hero.subtitle3")
          );
          setIndex(0);
          break;
        case 3:
          setTitle(
            sliderText[3]?.Title ? sliderText[3]?.Title : t("hero.title")
          );
          setSubtitle(
            sliderText[3]?.Content
              ? sliderText[3]?.Content
              : t("hero.subtitle4")
          );
          setIndex(0);
          break;
        default:
          setTitle(t("hero.title"));
          setSubtitle(t("hero.subtitle"));
          setIndex(1);
          break;
      }
    }, 4000);

    return () => clearInterval(interval);
  }, [index, t, sliderText]);

  return (
    <div className="h-[95vh] overflow-hidden">
      <QuizModal isOpen={isQuizModalOpen} setIsOpen={setIsQuizModalOpen} />
      <FsLightbox toggler={toggler} sources={[heroData?.VideoLink]} />
      <FsLightbox
        toggler={brochureToggler}
        sources={[heroData?.BrochureLink]}
        type={"image"}
      />
      {!imagesLoading && (
        <InfiniteZoomFader
          images={responseImages}
          zoom="out"
          zoomScale={0.75}
          zoomTime={5}
          zoomMax={0.25}
          zoomTimingFunction="linear"
          transitionTime={1}
        >
          <div className="bg-black/50 w-full h-full absolute top-0 left-0 " />
          <div
            id="home"
            className="container max-w-[1760px] xl:px-[30px] h-full flex items-center"
          >
            <div className="grid gap-[25px] items-center grid-cols-1 md:grid-cols-1 lg:grid-cols-2">
              <div className="space-y-[30px] md:space-y-[40px] lg:space-y-[30px] xl:space-y-[40px] lg:max-w-[590px]">
                <div className="min-h-[200px] md:min-h-[300px] ">
                  <h1
                    className={` text-[35px] md:text-[42px] lg:text-[40px] xl:text-[50px] 2xl:text-[55px] leading-[35px] md:leading-[46px] lg:leading-[55px] xl:leading-[64px] mb-[20px] md:mb-[30px] lg:mb-[20px] xl:mb-[40px] text-white`}
                    data-aos="fade-up"
                    data-aos-delay="100"
                    data-aos-duration="600"
                    data-aos-once="true"
                  >
                    {title}
                  </h1>

                  <p
                    className={`text-[16px] md:text-[18px] text-stone-300`}
                    data-aos="fade-up"
                    data-aos-delay="200"
                    data-aos-duration="600"
                    data-aos-once="true"
                  >
                    {subtitle}
                  </p>
                </div>

                <div
                  className="sm:flex items-center space-y-[20px] sm:space-y-[0] sm:space-x-[30px]"
                  data-aos="fade-up"
                  data-aos-delay="300"
                  data-aos-duration="600"
                  data-aos-once="true"
                >
                  <div className="flex flex-row  gap-[30px] ">
                    <Link
                      href="#contact"
                      className="inline-block text-white font-semibold text-[16px] md:text-[18px] transition duration-500 ease-in-out"
                    >
                      <ContactSupport size={48} className="mr-[3px] mb-[2px]" />
                      {t("hero.contact")}
                    </Link>

                    <div
                      className="flex items-center space-x-[10px] cursor-pointer group"
                      onClick={() => setToggler(!toggler)}
                    >
                      <div className="bg-[#fff] w-[36px] h-[36px] leading-[36px] pl-[5px] rounded-full text-center text-[22px] transition duration-500 ease-in-out group-hover:bg-primary-color group-hover:text-white">
                        <Play size={28} className="mr-[3px] mb-[2px]" />
                      </div>
                      <p className="font-semibold text-[16px] md:text-[18px] text-white">
                        {t("hero.howItWorks")}
                      </p>
                    </div>
                  </div>
                  <div
                    className="flex items-center space-x-[10px] cursor-pointer group"
                    onClick={() => setBrochureToggler(!brochureToggler)}
                  >
                    <p className="font-semibold text-[16px] md:text-[18px] text-sky-300 hover:text-sky-400 md:-ml-2 uppercase underline underline-offset-2">
                      <BookOpen size={20} className="mr-[6px] mb-[2px]" />
                      {t("hero.brochure")}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setIsQuizModalOpen(true)}
                  data-aos="fade-up"
                  data-aos-delay="400"
                  data-aos-duration="600"
                  data-aos-once="true"
                  className=" text-stone-200 font-semibold text-[16px] md:text-[18px] text-left italic underline underline-offset-2"
                >
                  <Quiz size={28} className="mr-[2px] inline" />
                  {t("hero.test")}...
                </button>
              </div>
            </div>
          </div>
        </InfiniteZoomFader>
      )}
    </div>
  );
};

export default HeroBanner;
