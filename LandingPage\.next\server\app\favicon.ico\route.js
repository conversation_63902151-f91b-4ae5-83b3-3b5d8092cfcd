"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/favicon.ico/route";
exports.ids = ["app/favicon.ico/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_metadata_route_loader_page_2Ffavicon_ico_2Froute_isDynamic_0_C_Users_burak_Desktop_OrthoClear_web_app_LandingPage_src_app_favicon_ico_next_metadata_route___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&isDynamic=0!./src/app/favicon.ico?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&isDynamic=0!./src/app/favicon.ico?__next_metadata_route__\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/favicon.ico/route\",\n        pathname: \"/favicon.ico\",\n        filename: \"favicon\",\n        bundlePath: \"app/favicon.ico/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&isDynamic=0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\favicon.ico?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Ffavicon_ico_2Froute_isDynamic_0_C_Users_burak_Desktop_OrthoClear_web_app_LandingPage_src_app_favicon_ico_next_metadata_route___WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/favicon.ico/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZmYXZpY29uLmljbyUyRnJvdXRlJnBhZ2U9JTJGZmF2aWNvbi5pY28lMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZmYXZpY29uLmljbyZhcHBEaXI9QyUzQSU1Q1VzZXJzJTVDYnVyYWslNUNEZXNrdG9wJTVDT3J0aG9DbGVhciU1Q3dlYi1hcHAlNUNMYW5kaW5nUGFnZSU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDYnVyYWslNUNEZXNrdG9wJTVDT3J0aG9DbGVhciU1Q3dlYi1hcHAlNUNMYW5kaW5nUGFnZSZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDMkk7QUFDMU07QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1R0FBdUc7QUFDL0c7QUFDaUo7O0FBRWpKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvPzlkMjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwibmV4dC1tZXRhZGF0YS1yb3V0ZS1sb2FkZXI/cGFnZT0lMkZmYXZpY29uLmljbyUyRnJvdXRlJmlzRHluYW1pYz0wIUM6XFxcXFVzZXJzXFxcXGJ1cmFrXFxcXERlc2t0b3BcXFxcT3J0aG9DbGVhclxcXFx3ZWItYXBwXFxcXExhbmRpbmdQYWdlXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX3JvdXRlX19cIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvZmF2aWNvbi5pY28vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2Zhdmljb24uaWNvXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcImZhdmljb25cIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvZmF2aWNvbi5pY28vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJuZXh0LW1ldGFkYXRhLXJvdXRlLWxvYWRlcj9wYWdlPSUyRmZhdmljb24uaWNvJTJGcm91dGUmaXNEeW5hbWljPTAhQzpcXFxcVXNlcnNcXFxcYnVyYWtcXFxcRGVza3RvcFxcXFxPcnRob0NsZWFyXFxcXHdlYi1hcHBcXFxcTGFuZGluZ1BhZ2VcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfcm91dGVfX1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2Zhdmljb24uaWNvL3JvdXRlXCI7XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCwgb3JpZ2luYWxQYXRobmFtZSwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&isDynamic=0!./src/app/favicon.ico?__next_metadata_route__":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&isDynamic=0!./src/app/favicon.ico?__next_metadata_route__ ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/server.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_server__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst contentType = \"image/x-icon\"\nconst buffer = Buffer.from(\"AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAMMOAADDDgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA1NTUANXV1Q3q6up+7Ozsm+3t7Zrn5+eb5ubmm+np6Zvp6emb6enpm+/v75vv7++b7+/vm/Hx8Zvv7++b7u7umOPj4zTw8PAAi4uLAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADZ2dkA19fXLOjo6Onp6en/6Ojo/9/f3//f39//4uLi/97e3v/f39//5OTk/+Pj4//j4+P/6Ojo/+Xl5f/k5OT/4ODgssfHxw7MzMwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAN3d3QDV1dVT4+Pj++Xl5f/i4uL/29vb/97e3v/d3d3/2NjY/9vb2//a2tr/2NjY/9nZ2f/f39//3d3d/9zc3P/b29v509PTZ/j4+ACzs7MAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACLi4sA9vb2ANfX14Hh4eH/4+Pj/9zc3P/Z2dn/3d3d/9XV1f/U1NT/1tbW/9DQ0P/Ozs7/zs7O/9ra2v/V1dX/09PT/9PT0//S0tLXzMzMJs/PzwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALi4uACsrKwF19fXrt/f3//f39//19fX/9ra2v/V1dX/0NDQ/9DQ0P/R0dH/xsbG/8TExP/ExMT/1dXV/8/Pz//Ly8v/y8vL/8vLy//MzMyWxcXFBcfHxwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAx8fHAMXFxRfX19fV3t7e/9vb2//V1dX/1tbW/8zMzP/Ly8v/y8vL/8vLy/+7u7v/urq6/7u7u//Q0ND/zc3N/8PDw//Dw8P/w8PD/8TExPDHx8dLysrKAK+vrwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADPz88AzMzMNtbW1u/d3d3/1dXV/9TU1P/Ly8v/x8fH/8fHx//IyMj/xcXF/7Gxsf+wsLD/srKy/8vLy//Nzc3/vLy8/7q6uv+6urr/urq6/7+/v8HIyMgWx8fHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANbW1gDOzs5f1NTU/tvb2//S0tL/zs7O/8LCwv/CwsL/wsLC/8PDw/+9vb3/pqam/6ampv+pqan/x8fH/8vLy/+5ubn/srKy/7Kysv+ysrL/tLS0/b6+vnkAAAAAxMTEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACUlJQA////AM3NzY7T09P/2dnZ/8/Pz//CwsL/vr6+/76+vv++vr7/wMDA/7W1tf+bm5v/m5ub/5+fn//Dw8P/yMjI/7q6uv+pqan/qqqq/6qqqv+qqqr/sLCw4r+/vzO8vLwAs7OzAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALy8vAC+vr4Jzc3NutLS0v/T09P/x8fH/7m5uf+6urr/ubm5/7m5uf+8vLz/qKio/5eXl/+RkZH/l5eX/8DAwP/Dw8P/vr6+/6Kiov+hoaH/oaGh/6Ghof+ioqL/r6+vqM3NzQrDw8MAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAyMjIAMfHxx/MzMze0dHR/8vLy//Hx8f/tra2/7W1tf+1tbX/t7e3/6ysrP91dXX/g4OD/5KSkv+RkZH/vb29/7+/v//BwcH/oqKi/5iYmP+ZmZn/mZmZ/5mZmf+enp72sLCwXaKiogCsrKwAAAAAAAAAAAAAAAAAAAAAAAAAAADLy8sAx8fHQcvLy/XPz8//xMTE/8jIyP+5ubn/sLCw/7Gxsf+0tLT/h4eH/2pqav9qamr/d3d3/5mZmf++vr7/u7u7/76+vv+oqKj/kJCQ/5GRkf+QkJD/kZGR/5aWlv+vr6+21tbWCsHBwQAAAAAAAAAAAAAAAAAAAAAAbGxsAM/PzwDHx8dtysrK/8rKyv/AwMD/wsLC/8DAwP+srKz/r6+v/5+fn/9tbW3/ampq/3Nzc/+Ojo7/sbGx/7u7u/+4uLj/urq6/7CwsP+JiYn/iIiI/46Ojv+Tk5P/mJiY7La2tlM3NzcAxMTEAAAAAAAAAAAAAAAAAAAAAACrq6sAcnJyAMbGxpzKysr/xMTE/729vf+9vb3/wsLC/6+vr/+pqan/e3t7/3V1df+Pj4//rq6u/729vf+1tbX/tLS0/7S0tP+0tLT/tbW1/5CQkP+Ojo7/iIiI/39/f/aWlpZt////Arq6ugAAAAAAAAAAAAAAAAAAAAAAAAAAAL+/vwDCwsIOxcXFxsnJyf+9vb3/u7u7/7u7u/+9vb3/vLy8/6Kiov+VlZX/qKio/6ysrP+zs7P7ubm5+LCwsP+xsbH/sbGx/7Gxsf+1tbX/l5eX/3R0dP9xcXH8j4+Pidra2ge8vLwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAxcXFAMPDwyjExMTmxcXF/7i4uP+4uLj/uLi4/7i4uP/BwcH/t7e3/6ampv+kpKT/o6Oj/66urtPBwcF7s7Oz7a2trf+tra3/ra2t/6+vr/97e3v/bW1t/4qKiqTBwcEPsbGxAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADFxcUAwsLCTcTExPm+vr7/tbW1/7W1tf+1tbX/tbW1/7i4uP+rq6v/np6e/5+fn/+fn5//q6urttTU1AnAwMBdsbGx6aqqqv+rq6v/p6en/3R0dP+EhIS9s7OzHaWlpQD///8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAaGhoAMbGxgDAwMB7w8PD/7e3t/+ysrL/srKy/7Ozs/+ysrL/tra2/6Ojo/+ZmZn/mpqa/5ubm/+qqqqYFBQUAP///wG/v79Sr6+v4qioqP+dnZ3/goKC06mpqS6SkpIA9PT0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACvr68Avr6+A76+vqm/v7//u7u7/7CwsP+wsLD/sLCw/7CwsP+0tLT/m5ub/5WVlf+VlZX/l5eX/6urq3mcnJwAzc3NAAAAAAC9vb1Gr6+v3KSkpOWenp5Ebm5uANzc3AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMDAwADCwsIUu7u70Le3t/++vr7/tbW1/6ysrP+tra3/rq6u/6+vr/+Tk5P/kJCQ/5CQkP+UlJT9rKysWaioqAAAAAAAzMzMALS0tADDw8M7xcXFTLq6ugHOzs4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwsLCAL+/vy64uLjrtbW1/7a2tv+8vLz/r6+v/6qqqv+rq6v/qamp/4yMjP+Li4v/i4uL/5aWlu6xsbE5sbGxAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADDw8MAwsLCG7m5udKzs7P/s7Oz/7e3t/+7u7v/qamp/6qqqv+hoaH/hoaG/4iIiP+UlJTeqqqqY8rKygfBwcEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAL29vQCtra0Avr6+dLKysv6wsLD/s7Oz/62trf+rq6v/qqqq/5aWlv+Ghob7mZmZsbOzszD///8A1tbWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMLCwgDDw8MftbW11a6urv+xsbH/o6Oj/5qamv+vr6//nZ2d756ennjDw8MPsLCwAP///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAu7u7AJ2dnQC6urp5rq6u/6+vr/+hoaH/i4uL/6CgoP+9vb2G8fHxAsnJyQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwcHBAMLCwiKxsbHZrKys/52dnf+Hh4f/l5eXzri4uB60tLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAwMAAaGhoALi4uH6srKz/mpqa/4qKivenp6ddhoaGAMfHxwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC/v78Av7+/JbGxsdyYmJj/lpaWrcrKygu7u7sAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAALy8vAA2NjYAuLi4g6Wlpeinp6c5oqKiAN/f3wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMrKygDIyMguxcXFdK+vrwTCwsIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA///////////+AAH//gAA//4AAP/+AAB//AAAP/wAAD/8AAAf/AAAH/wAAA/4AAAH+AAAB/gAAAP4AAAH+AAAB/AAAA/wAAAf8AAAP/AAQH/gAHD/4AB4/+AAf//gAH//8AH///AD///4B///+A////wf///8H////j////4///8=\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Ffavicon.ico%2Froute&isDynamic=0!./src/app/favicon.ico?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ffavicon.ico%2Froute&page=%2Ffavicon.ico%2Froute&appPaths=&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();