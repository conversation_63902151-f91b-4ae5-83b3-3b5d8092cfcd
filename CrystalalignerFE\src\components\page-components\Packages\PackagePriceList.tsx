import React, { Suspense } from 'react'
import { FaFileDownload } from 'react-icons/fa'
import useCurrency from '@/utils/hooks/useCurrency'

const PackagePriceList = () => {
    const { currency, countryCode, isLoading: currencyLoading } = useCurrency()

    const currentYear = new Date().getFullYear()

    return (
        <Suspense
            fallback={
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-gray-900"></div>
                </div>
            }
        >
            <div className="flex gap-8">
                {(countryCode === 'TR' || !countryCode || currencyLoading) && (
                    <a
                        href="https://crystalaligner.com/files/Resources/Uploads/Files/fiyat_listesi.pdf"
                        target="blank"
                        rel="norefferer"
                        className="flex flex-col gap-4 items-center text-center bg-gray-100 p-8 rounded-lg shadow-lg w-64"
                    >
                        <FaFileDownload size={64} />
                        <h3 className="font-light flex flex-col gap-2">
                            {currentYear} <br></br> Fiyat Tarifesi
                            <span className="font-bold ">-TRY-</span>
                        </h3>
                    </a>
                )}
                {countryCode !== 'TR' &&
                    countryCode !== null &&
                    countryCode && !currencyLoading && (
                        <a
                            href="https://crystalaligner.com/files/Resources/Uploads/Files/price_list.pdf"
                            target="blank"
                            rel="norefferer"
                            className="flex flex-col gap-4 items-center text-center bg-gray-100 p-8 rounded-lg shadow-lg w-64"
                        >
                            <FaFileDownload size={64} />
                            <h3 className="font-light flex flex-col gap-2">
                                {currentYear} <br></br>Price List
                                <span className="font-bold ">-EUR-</span>
                            </h3>
                        </a>
                    )}
            </div>
        </Suspense>
    )
}

export default PackagePriceList
