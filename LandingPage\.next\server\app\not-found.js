/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\layout.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/AosAnimation.tsx */ \"(ssr)/./src/components/Layout/AosAnimation.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/BackToTop.tsx */ \"(ssr)/./src/components/Layout/BackToTop.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/TosterProvider.tsx */ \"(ssr)/./src/providers/TosterProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cnot-found.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cnot-found.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDYnVyYWslNUNEZXNrdG9wJTVDT3J0aG9DbGVhciU1Q3dlYi1hcHAlNUNMYW5kaW5nUGFnZSU1Q3NyYyU1Q2FwcCU1Q25vdC1mb3VuZC50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvPzU1M2UiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxidXJha1xcXFxEZXNrdG9wXFxcXE9ydGhvQ2xlYXJcXFxcd2ViLWFwcFxcXFxMYW5kaW5nUGFnZVxcXFxzcmNcXFxcYXBwXFxcXG5vdC1mb3VuZC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cnot-found.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFoundPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Error_Custom404__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Error/Custom404 */ \"(ssr)/./src/components/Error/Custom404.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NotFoundPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Error_Custom404__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFcUQ7QUFFdEMsU0FBU0M7SUFDdEIscUJBQ0U7a0JBQ0UsNEVBQUNELG1FQUFTQTs7Ozs7O0FBR2hCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9zcmMvYXBwL25vdC1mb3VuZC50c3g/Y2FlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBDdXN0b200MDQgZnJvbSAnQC9jb21wb25lbnRzL0Vycm9yL0N1c3RvbTQwNCc7XHJcbiBcclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmRQYWdlKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8Q3VzdG9tNDA0IC8+XHJcbiAgICA8Lz5cclxuICApXHJcbn0iXSwibmFtZXMiOlsiQ3VzdG9tNDA0IiwiTm90Rm91bmRQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Error/Custom404.tsx":
/*!********************************************!*\
  !*** ./src/components/Error/Custom404.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _public_images_404_error_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../public/images/404-error.png */ \"(ssr)/./public/images/404-error.png\");\n/* harmony import */ var _public_images_shape2_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../../../../public/images/shape2.png */ \"(ssr)/./public/images/shape2.png\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Custom404 = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-[9999999] bg-[#F8F6F5] text-center h-[100vh] max-h-[100vh] overflow-auto py-[50px] md:py-[60px] lg:py-[70px] xl:py-[80px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            src: _public_images_404_error_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                            alt: \"error-image\",\n                            className: \"inline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-black text-[25px] lg:text-[30px] font-bold mt-[40px] mb-[15px] leading-[1.3]\",\n                            children: \"Oops! That page can not be found.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-[14px] md:text-[15px] lg:text-[16px] md:max-w-[540px] text-[#4c4c4c] leading-[1.7] ml-auto mr-auto mb-[15px] lg:mb-[18px]\",\n                            children: \"The page you are looking for might have been removed had its name changed or is temporarily unavailable.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"py-[15px] px-[40px] inline-block rounded-[6px] bg-primary-color text-white font-semibold text-[16px] md:text-[18px] transition duration-500 ease-in-out hover:bg-black-color\",\n                            children: \"Back To Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: _public_images_shape2_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    alt: \"shape\",\n                    className: \"absolute top-0 right-0 max-w-[120px] lg:max-w-[150px] xl:max-w-[200px] 2xl:max-w-[250px] hidden sm:block\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Error\\\\Custom404.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Custom404);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Error/Custom404.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/AosAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/Layout/AosAnimation.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var aos__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! aos */ \"(ssr)/./node_modules/aos/dist/aos.js\");\n/* harmony import */ var aos__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(aos__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _node_modules_aos_dist_aos_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../node_modules/aos/dist/aos.css */ \"(ssr)/./node_modules/aos/dist/aos.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AosAnimation = ()=>{\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        aos__WEBPACK_IMPORTED_MODULE_2___default().init();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Layout\\\\AosAnimation.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AosAnimation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9MYXlvdXQvQW9zQW5pbWF0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFMEI7QUFDSjtBQUMwQjtBQUVoRCxNQUFNRSxlQUFlO0lBRW5CRixzREFBZSxDQUFDO1FBQ2RDLCtDQUFRO0lBQ1YsR0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNJOzs7OztBQUVMO0FBRUEsaUVBQWVILFlBQVlBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcnlzdGFsYWxpZ25lci8uL3NyYy9jb21wb25lbnRzL0xheW91dC9Bb3NBbmltYXRpb24udHN4PzlmYjIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBBT1MgZnJvbSBcImFvc1wiO1xyXG5pbXBvcnQgXCIuLi8uLi8uLi9ub2RlX21vZHVsZXMvYW9zL2Rpc3QvYW9zLmNzc1wiO1xyXG4gIFxyXG5jb25zdCBBb3NBbmltYXRpb24gPSAoKSA9PiB7XHJcbiBcclxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgQU9TLmluaXQoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2PjwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBBb3NBbmltYXRpb247Il0sIm5hbWVzIjpbIlJlYWN0IiwiQU9TIiwiQW9zQW5pbWF0aW9uIiwidXNlRWZmZWN0IiwiaW5pdCIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/AosAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout/BackToTop.tsx":
/*!*********************************************!*\
  !*** ./src/components/Layout/BackToTop.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst BackToTop = ()=>{\n    const [showScroll, setShowScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.addEventListener(\"scroll\", checkScrollTop);\n        return function cleanup() {\n            window.removeEventListener(\"scroll\", checkScrollTop);\n        };\n    });\n    const checkScrollTop = ()=>{\n        if (!showScroll && window.pageYOffset > 100) {\n            setShowScroll(true);\n        } else if (showScroll && window.pageYOffset <= 100) {\n            setShowScroll(false);\n        }\n    };\n    const scrollTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            onClick: scrollTop,\n            style: {\n                display: showScroll ? \"block\" : \"none\"\n            },\n            className: \"bg-primary-color text-white fixed right-0 bottom-[30px] cursor-pointer w-[40px] h-[35px] rounded-l-full text-center leading-[35px] text-[20px] z-50 hover:bg-[#000]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                className: \"ri-arrow-up-line\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Layout\\\\BackToTop.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\Layout\\\\BackToTop.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackToTop);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout/BackToTop.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/TosterProvider.tsx":
/*!******************************************!*\
  !*** ./src/providers/TosterProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst TosterProvider = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\providers\\\\TosterProvider.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TosterProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL1Rvc3RlclByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQzBCO0FBQ2dCO0FBRTFDLE1BQU1FLGlCQUFpQjtJQUN0QixxQkFBTyw4REFBQ0Qsb0RBQU9BOzs7OztBQUNoQjtBQUVBLGlFQUFlQyxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9zcmMvcHJvdmlkZXJzL1Rvc3RlclByb3ZpZGVyLnRzeD8yNDRhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tIFwicmVhY3QtaG90LXRvYXN0XCI7XHJcblxyXG5jb25zdCBUb3N0ZXJQcm92aWRlciA9ICgpID0+IHtcclxuXHRyZXR1cm4gPFRvYXN0ZXIgLz47XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBUb3N0ZXJQcm92aWRlcjsiXSwibmFtZXMiOlsiUmVhY3QiLCJUb2FzdGVyIiwiVG9zdGVyUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/TosterProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./public/css/navbar.css":
/*!*******************************!*\
  !*** ./public/css/navbar.css ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c277b033a41d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wdWJsaWMvY3NzL25hdmJhci5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcnlzdGFsYWxpZ25lci8uL3B1YmxpYy9jc3MvbmF2YmFyLmNzcz81NzU5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzI3N2IwMzNhNDFkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./public/css/navbar.css\n");

/***/ }),

/***/ "(rsc)/./public/fonts/flaticon_mycollection.css":
/*!************************************************!*\
  !*** ./public/fonts/flaticon_mycollection.css ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9ea580f163d4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wdWJsaWMvZm9udHMvZmxhdGljb25fbXljb2xsZWN0aW9uLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vcHVibGljL2ZvbnRzL2ZsYXRpY29uX215Y29sbGVjdGlvbi5jc3M/YTlkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjllYTU4MGYxNjNkNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./public/fonts/flaticon_mycollection.css\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f3ad408e9e7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzczZDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZjNhZDQwOGU5ZTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Mukta_arguments_weight_200_300_400_500_600_700_800_subsets_latin_display_swap_variableName_mukta___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Mukta\",\"arguments\":[{\"weight\":[\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"mukta\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Mukta\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"mukta\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Mukta_arguments_weight_200_300_400_500_600_700_800_subsets_latin_display_swap_variableName_mukta___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Mukta_arguments_weight_200_300_400_500_600_700_800_subsets_latin_display_swap_variableName_mukta___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var remixicon_fonts_remixicon_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remixicon/fonts/remixicon.css */ \"(rsc)/./node_modules/remixicon/fonts/remixicon.css\");\n/* harmony import */ var _public_css_navbar_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/css/navbar.css */ \"(rsc)/./public/css/navbar.css\");\n/* harmony import */ var _public_fonts_flaticon_mycollection_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/fonts/flaticon_mycollection.css */ \"(rsc)/./public/fonts/flaticon_mycollection.css\");\n/* harmony import */ var swiper_css_bundle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/css/bundle */ \"(rsc)/./node_modules/swiper/swiper-bundle.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Layout_AosAnimation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Layout/AosAnimation */ \"(rsc)/./src/components/Layout/AosAnimation.tsx\");\n/* harmony import */ var _components_Layout_BackToTop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Layout/BackToTop */ \"(rsc)/./src/components/Layout/BackToTop.tsx\");\n/* harmony import */ var _providers_TosterProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/TosterProvider */ \"(rsc)/./src/providers/TosterProvider.tsx\");\n\n\n\n\n\n\n// Globals Styles\n\n\n\n\nconst metadata = {\n    title: \"Crystal Aligner - Always Smile and Smile Brightly\",\n    description: \"Crystal, tedavilerinize hız ve kolaylık katmak i\\xe7in burada! Hızlı 3D analizimiz ve yapım s\\xfcrecimiz ile birlikte tedavinize 15 g\\xfcn i\\xe7erisinde başlama imkanı sunuyoruz.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Mukta_arguments_weight_200_300_400_500_600_700_800_subsets_latin_display_swap_variableName_mukta___WEBPACK_IMPORTED_MODULE_9___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_TosterProvider__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_AosAnimation__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_BackToTop__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBWU1BO0FBWmlDO0FBQ0Y7QUFDaUI7QUFDM0I7QUFDM0IsaUJBQWlCO0FBQ007QUFFcUM7QUFDTjtBQUNFO0FBU2pELE1BQU1JLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsYUFDRTtBQUNKLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1osK01BQWU7OzhCQUM5Qiw4REFBQ0csaUVBQWNBOzs7OztnQkFFZEs7OEJBR0QsOERBQUNQLHVFQUFZQTs7Ozs7OEJBR2IsOERBQUNDLG9FQUFTQTs7Ozs7Ozs7Ozs7Ozs7OztBQUlsQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwicmVtaXhpY29uL2ZvbnRzL3JlbWl4aWNvbi5jc3NcIjtcclxuaW1wb3J0IFwiLi4vLi4vcHVibGljL2Nzcy9uYXZiYXIuY3NzXCI7XHJcbmltcG9ydCBcIi4uLy4uL3B1YmxpYy9mb250cy9mbGF0aWNvbl9teWNvbGxlY3Rpb24uY3NzXCI7XHJcbmltcG9ydCBcInN3aXBlci9jc3MvYnVuZGxlXCI7XHJcbi8vIEdsb2JhbHMgU3R5bGVzXHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcclxuXHJcbmltcG9ydCBBb3NBbmltYXRpb24gZnJvbSBcIkAvY29tcG9uZW50cy9MYXlvdXQvQW9zQW5pbWF0aW9uXCI7XHJcbmltcG9ydCBCYWNrVG9Ub3AgZnJvbSBcIkAvY29tcG9uZW50cy9MYXlvdXQvQmFja1RvVG9wXCI7XHJcbmltcG9ydCBUb3N0ZXJQcm92aWRlciBmcm9tIFwiQC9wcm92aWRlcnMvVG9zdGVyUHJvdmlkZXJcIjtcclxuaW1wb3J0IHsgTXVrdGEgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5cclxuY29uc3QgbXVrdGEgPSBNdWt0YSh7XHJcbiAgd2VpZ2h0OiBbXCIyMDBcIiwgXCIzMDBcIiwgXCI0MDBcIiwgXCI1MDBcIiwgXCI2MDBcIiwgXCI3MDBcIiwgXCI4MDBcIl0sXHJcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXHJcbiAgZGlzcGxheTogXCJzd2FwXCIsXHJcbn0pO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xyXG4gIHRpdGxlOiBcIkNyeXN0YWwgQWxpZ25lciAtIEFsd2F5cyBTbWlsZSBhbmQgU21pbGUgQnJpZ2h0bHlcIixcclxuICBkZXNjcmlwdGlvbjpcclxuICAgIFwiQ3J5c3RhbCwgdGVkYXZpbGVyaW5pemUgaMSxeiB2ZSBrb2xheWzEsWsga2F0bWFrIGnDp2luIGJ1cmFkYSEgSMSxemzEsSAzRCBhbmFsaXppbWl6IHZlIHlhcMSxbSBzw7xyZWNpbWl6IGlsZSBiaXJsaWt0ZSB0ZWRhdmluaXplIDE1IGfDvG4gacOnZXJpc2luZGUgYmHFn2xhbWEgaW1rYW7EsSBzdW51eW9ydXouXCIsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17bXVrdGEuY2xhc3NOYW1lfT5cclxuICAgICAgICA8VG9zdGVyUHJvdmlkZXIgLz5cclxuXHJcbiAgICAgICAge2NoaWxkcmVufVxyXG5cclxuICAgICAgICB7LyogQW9zQW5pbWF0aW9uICovfVxyXG4gICAgICAgIDxBb3NBbmltYXRpb24gLz5cclxuXHJcbiAgICAgICAgey8qIEJhY2tUb1RvcCAqL31cclxuICAgICAgICA8QmFja1RvVG9wIC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJtdWt0YSIsIkFvc0FuaW1hdGlvbiIsIkJhY2tUb1RvcCIsIlRvc3RlclByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    // Or a custom loading skeleton component\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 left-0 bg-white w-full h-full z-[9999] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-[80px] w-[80px] rounded-full border-t-8 border-b-8 border-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\loading.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 h-[80px] w-[80px] rounded-full border-t-8 border-b-8 border-primary-color animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\loading.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"text-[16px] mt-[10px]\",\n                        children: \"Crystal Aligner\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\loading.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\loading.tsx\",\n                lineNumber: 6,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 5,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0Qix5Q0FBeUM7SUFDekMscUJBQ0U7a0JBQ0UsNEVBQUNDO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLaEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcnlzdGFsYWxpZ25lci8uL3NyYy9hcHAvbG9hZGluZy50c3g/OWNkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xyXG4gIC8vIE9yIGEgY3VzdG9tIGxvYWRpbmcgc2tlbGV0b24gY29tcG9uZW50XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTAgbGVmdC0wIGJnLXdoaXRlIHctZnVsbCBoLWZ1bGwgei1bOTk5OV0gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtWzgwcHhdIHctWzgwcHhdIHJvdW5kZWQtZnVsbCBib3JkZXItdC04IGJvcmRlci1iLTggYm9yZGVyLWdyYXktMjAwXCI+PC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCBoLVs4MHB4XSB3LVs4MHB4XSByb3VuZGVkLWZ1bGwgYm9yZGVyLXQtOCBib3JkZXItYi04IGJvcmRlci1wcmltYXJ5LWNvbG9yIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxyXG4gICAgICAgICAgPGg2IGNsYXNzTmFtZT1cInRleHQtWzE2cHhdIG10LVsxMHB4XVwiPkNyeXN0YWwgQWxpZ25lcjwvaDY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsImg2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\OrthoClear\web-app\LandingPage\src\app\not-found.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Layout/AosAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/Layout/AosAnimation.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\OrthoClear\web-app\LandingPage\src\components\Layout\AosAnimation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Layout/BackToTop.tsx":
/*!*********************************************!*\
  !*** ./src/components/Layout/BackToTop.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\OrthoClear\web-app\LandingPage\src\components\Layout\BackToTop.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/providers/TosterProvider.tsx":
/*!******************************************!*\
  !*** ./src/providers/TosterProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\OrthoClear\web-app\LandingPage\src\providers\TosterProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(ssr)/./public/images/404-error.png":
/*!*************************************!*\
  !*** ./public/images/404-error.png ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/404-error.38100680.png\",\"height\":533,\"width\":550,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2F404-error.38100680.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wdWJsaWMvaW1hZ2VzLzQwNC1lcnJvci5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9wdWJsaWMvaW1hZ2VzLzQwNC1lcnJvci5wbmc/MGUyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvNDA0LWVycm9yLjM4MTAwNjgwLnBuZ1wiLFwiaGVpZ2h0XCI6NTMzLFwid2lkdGhcIjo1NTAsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGNDA0LWVycm9yLjM4MTAwNjgwLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./public/images/404-error.png\n");

/***/ }),

/***/ "(ssr)/./public/images/shape2.png":
/*!**********************************!*\
  !*** ./public/images/shape2.png ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/shape2.193da144.png\",\"height\":368,\"width\":368,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshape2.193da144.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9wdWJsaWMvaW1hZ2VzL3NoYXBlMi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsZ01BQWdNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9wdWJsaWMvaW1hZ2VzL3NoYXBlMi5wbmc/MmYxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvc2hhcGUyLjE5M2RhMTQ0LnBuZ1wiLFwiaGVpZ2h0XCI6MzY4LFwid2lkdGhcIjozNjgsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGc2hhcGUyLjE5M2RhMTQ0LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./public/images/shape2.png\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcnlzdGFsYWxpZ25lci8uL3NyYy9hcHAvZmF2aWNvbi5pY28/YzY5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIzMngzMlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/swiper","vendor-chunks/aos","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/remixicon"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();