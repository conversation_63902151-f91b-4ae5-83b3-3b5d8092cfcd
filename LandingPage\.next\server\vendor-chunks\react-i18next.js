"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-i18next";
exports.ids = ["vendor-chunks/react-i18next"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/I18nextProvider.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nextProvider: () => (/* binding */ I18nextProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nfunction I18nextProvider(_ref) {\n  let {\n    i18n,\n    defaultNS,\n    children\n  } = _ref;\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext.Provider, {\n    value\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL0kxOG5leHRQcm92aWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDSjtBQUNwQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLGdCQUFnQiw4Q0FBTztBQUN2QjtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsb0RBQWEsQ0FBQyxvREFBVztBQUNsQztBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWkxOG5leHQvZGlzdC9lcy9JMThuZXh0UHJvdmlkZXIuanM/NTgzOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVFbGVtZW50LCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSTE4bkNvbnRleHQgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIEkxOG5leHRQcm92aWRlcihfcmVmKSB7XG4gIGxldCB7XG4gICAgaTE4bixcbiAgICBkZWZhdWx0TlMsXG4gICAgY2hpbGRyZW5cbiAgfSA9IF9yZWY7XG4gIGNvbnN0IHZhbHVlID0gdXNlTWVtbygoKSA9PiAoe1xuICAgIGkxOG4sXG4gICAgZGVmYXVsdE5TXG4gIH0pLCBbaTE4biwgZGVmYXVsdE5TXSk7XG4gIHJldHVybiBjcmVhdGVFbGVtZW50KEkxOG5Db250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWVcbiAgfSwgY2hpbGRyZW4pO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/Trans.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Trans.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\nfunction Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_2__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const t = tFromProps || i18n && i18n.t.bind(i18n);\n  return (0,_TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans)({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t && t.ns || defaultNSFromContext || i18n && i18n.options && i18n.options.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/Trans.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/TransWithoutContext.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* binding */ nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html-parse-stringify */ \"(ssr)/./node_modules/html-parse-stringify/dist/html-parse-stringify.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\n\n\n\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\nfunction getChildren(node) {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n}\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(child => (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child));\n}\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\nfunction mergeProps(source, target) {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\nfunction nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (typeof child === 'string') {\n      stringNode += `${child}`;\n    } else if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) {\n      const childPropsCount = Object.keys(child.props).length;\n      const shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      const childChildren = child.props.children;\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += `<${child.type}/>`;\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += `<${child.type}>${childChildren}</${child.type}>`;\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      }\n    } else if (child === null) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(`Trans: the passed in value is invalid - seems you passed in a null child.`);\n    } else if (typeof child === 'object') {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n      } else {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(`react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.`, child);\n      }\n    } else {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(`Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.`, child);\n    }\n  });\n  return stringNode;\n}\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  function getData(childs) {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (typeof child === 'object' && !(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) Object.assign(data, child);\n    });\n  }\n  getData(children);\n  const ast = html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  function renderInner(child, node, rootReactNode) {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  }\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...react__WEBPACK_IMPORTED_MODULE_0__.Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(c.type, {\n          ...props,\n          key: i,\n          ref: c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  }\n  function mapAST(reactNode, astNode, rootReactNode) {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && typeof child === 'object' && child.dummy && !isElement;\n        const isKnownComponent = typeof children === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n        if (typeof child === 'string') {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (typeof child === 'object' && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  }\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\nfunction Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const i18n = i18nFromProps || (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__.getI18n)();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  if (context) tOptions.context = context;\n  const reactI18nextOptions = {\n    ...(0,_defaults_js__WEBPACK_IMPORTED_MODULE_3__.getDefaults)(),\n    ...(i18n.options && i18n.options.react)\n  };\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(`${c}/>`) < 0 && translation.indexOf(`${c} />`) < 0) return;\n      function Componentized() {\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, comp);\n      }\n      components[c] = (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Componentized);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(useAsParent, additionalProps, content) : content;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL1RyYW5zV2l0aG91dENvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RjtBQUNoRDtBQUNJO0FBQ0E7QUFDQTtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxxREFBYztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsTUFBTTtBQUM3QixNQUFNLFNBQVMscURBQWM7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsV0FBVztBQUNyQyxRQUFRO0FBQ1IsMEJBQTBCLFdBQVcsS0FBSyxXQUFXO0FBQ3JELFFBQVE7QUFDUiwwQkFBMEIsV0FBVyxLQUFLLFdBQVc7QUFDckQsUUFBUTtBQUNSLDBCQUEwQixXQUFXLEdBQUcsY0FBYyxJQUFJLFdBQVc7QUFDckUsUUFBUTtBQUNSO0FBQ0EsMEJBQTBCLFdBQVcsR0FBRyxRQUFRLElBQUksV0FBVztBQUMvRDtBQUNBLE1BQU07QUFDTixNQUFNLCtDQUFJO0FBQ1YsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0Esa0NBQWtDLFFBQVEsSUFBSSxPQUFPO0FBQ3JELHlCQUF5QixFQUFFLFFBQVE7QUFDbkMsUUFBUTtBQUNSLFFBQVEsK0NBQUksd0dBQXdHLGlCQUFpQjtBQUNySTtBQUNBLE1BQU07QUFDTixNQUFNLCtDQUFJLCtFQUErRSxRQUFRLG9FQUFvRSxRQUFRO0FBQzdLO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RkFBNkYsS0FBSztBQUNsRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsdUNBQXVDLHFEQUFjO0FBQy9HLEtBQUs7QUFDTDtBQUNBO0FBQ0EsY0FBYyx1REFBVSxPQUFPLGFBQWE7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtREFBWTtBQUMzQjtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ04sa0JBQWtCLDJDQUFRO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvREFBYTtBQUM1QjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDBCQUEwQixxREFBYztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBLHVCQUF1QixvREFBYTtBQUNwQyx3QkFBd0IsVUFBVSxHQUFHLEVBQUU7QUFDdkMsZUFBZTtBQUNmLGNBQWM7QUFDZDtBQUNBLHVCQUF1QixvREFBYTtBQUNwQyx3QkFBd0IsVUFBVSxHQUFHLEVBQUU7QUFDdkMsZUFBZTtBQUNmO0FBQ0EsWUFBWTtBQUNaLHlCQUF5QixXQUFXO0FBQ3BDLFlBQVk7QUFDWjtBQUNBLHlCQUF5QixVQUFVLEdBQUcsTUFBTSxJQUFJLFVBQVU7QUFDMUQ7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixvREFBYTtBQUNoQyxvQkFBb0IsVUFBVSxHQUFHLEVBQUU7QUFDbkMsV0FBVztBQUNYLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixnQ0FBZ0MseURBQU87QUFDdkM7QUFDQSxJQUFJLG1EQUFRO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8seURBQVc7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkdBQTJHLEVBQUUsbUNBQW1DLEdBQUc7QUFDbko7QUFDQSxlQUFlLG9EQUFhLENBQUMsMkNBQVE7QUFDckM7QUFDQSxzQkFBc0Isb0RBQWE7QUFDbkMsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixvREFBYTtBQUNwQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWkxOG5leHQvZGlzdC9lcy9UcmFuc1dpdGhvdXRDb250ZXh0LmpzP2Q2ZDMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRnJhZ21lbnQsIGlzVmFsaWRFbGVtZW50LCBjbG9uZUVsZW1lbnQsIGNyZWF0ZUVsZW1lbnQsIENoaWxkcmVuIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IEhUTUwgZnJvbSAnaHRtbC1wYXJzZS1zdHJpbmdpZnknO1xuaW1wb3J0IHsgd2Fybiwgd2Fybk9uY2UgfSBmcm9tICcuL3V0aWxzLmpzJztcbmltcG9ydCB7IGdldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5pbXBvcnQgeyBnZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuZnVuY3Rpb24gaGFzQ2hpbGRyZW4obm9kZSwgY2hlY2tMZW5ndGgpIHtcbiAgaWYgKCFub2RlKSByZXR1cm4gZmFsc2U7XG4gIGNvbnN0IGJhc2UgPSBub2RlLnByb3BzID8gbm9kZS5wcm9wcy5jaGlsZHJlbiA6IG5vZGUuY2hpbGRyZW47XG4gIGlmIChjaGVja0xlbmd0aCkgcmV0dXJuIGJhc2UubGVuZ3RoID4gMDtcbiAgcmV0dXJuICEhYmFzZTtcbn1cbmZ1bmN0aW9uIGdldENoaWxkcmVuKG5vZGUpIHtcbiAgaWYgKCFub2RlKSByZXR1cm4gW107XG4gIGNvbnN0IGNoaWxkcmVuID0gbm9kZS5wcm9wcyA/IG5vZGUucHJvcHMuY2hpbGRyZW4gOiBub2RlLmNoaWxkcmVuO1xuICByZXR1cm4gbm9kZS5wcm9wcyAmJiBub2RlLnByb3BzLmkxOG5Jc0R5bmFtaWNMaXN0ID8gZ2V0QXNBcnJheShjaGlsZHJlbikgOiBjaGlsZHJlbjtcbn1cbmZ1bmN0aW9uIGhhc1ZhbGlkUmVhY3RDaGlsZHJlbihjaGlsZHJlbikge1xuICBpZiAoT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGNoaWxkcmVuKSAhPT0gJ1tvYmplY3QgQXJyYXldJykgcmV0dXJuIGZhbHNlO1xuICByZXR1cm4gY2hpbGRyZW4uZXZlcnkoY2hpbGQgPT4gaXNWYWxpZEVsZW1lbnQoY2hpbGQpKTtcbn1cbmZ1bmN0aW9uIGdldEFzQXJyYXkoZGF0YSkge1xuICByZXR1cm4gQXJyYXkuaXNBcnJheShkYXRhKSA/IGRhdGEgOiBbZGF0YV07XG59XG5mdW5jdGlvbiBtZXJnZVByb3BzKHNvdXJjZSwgdGFyZ2V0KSB7XG4gIGNvbnN0IG5ld1RhcmdldCA9IHtcbiAgICAuLi50YXJnZXRcbiAgfTtcbiAgbmV3VGFyZ2V0LnByb3BzID0gT2JqZWN0LmFzc2lnbihzb3VyY2UucHJvcHMsIHRhcmdldC5wcm9wcyk7XG4gIHJldHVybiBuZXdUYXJnZXQ7XG59XG5leHBvcnQgZnVuY3Rpb24gbm9kZXNUb1N0cmluZyhjaGlsZHJlbiwgaTE4bk9wdGlvbnMpIHtcbiAgaWYgKCFjaGlsZHJlbikgcmV0dXJuICcnO1xuICBsZXQgc3RyaW5nTm9kZSA9ICcnO1xuICBjb25zdCBjaGlsZHJlbkFycmF5ID0gZ2V0QXNBcnJheShjaGlsZHJlbik7XG4gIGNvbnN0IGtlZXBBcnJheSA9IGkxOG5PcHRpb25zLnRyYW5zU3VwcG9ydEJhc2ljSHRtbE5vZGVzICYmIGkxOG5PcHRpb25zLnRyYW5zS2VlcEJhc2ljSHRtbE5vZGVzRm9yID8gaTE4bk9wdGlvbnMudHJhbnNLZWVwQmFzaWNIdG1sTm9kZXNGb3IgOiBbXTtcbiAgY2hpbGRyZW5BcnJheS5mb3JFYWNoKChjaGlsZCwgY2hpbGRJbmRleCkgPT4ge1xuICAgIGlmICh0eXBlb2YgY2hpbGQgPT09ICdzdHJpbmcnKSB7XG4gICAgICBzdHJpbmdOb2RlICs9IGAke2NoaWxkfWA7XG4gICAgfSBlbHNlIGlmIChpc1ZhbGlkRWxlbWVudChjaGlsZCkpIHtcbiAgICAgIGNvbnN0IGNoaWxkUHJvcHNDb3VudCA9IE9iamVjdC5rZXlzKGNoaWxkLnByb3BzKS5sZW5ndGg7XG4gICAgICBjb25zdCBzaG91bGRLZWVwQ2hpbGQgPSBrZWVwQXJyYXkuaW5kZXhPZihjaGlsZC50eXBlKSA+IC0xO1xuICAgICAgY29uc3QgY2hpbGRDaGlsZHJlbiA9IGNoaWxkLnByb3BzLmNoaWxkcmVuO1xuICAgICAgaWYgKCFjaGlsZENoaWxkcmVuICYmIHNob3VsZEtlZXBDaGlsZCAmJiBjaGlsZFByb3BzQ291bnQgPT09IDApIHtcbiAgICAgICAgc3RyaW5nTm9kZSArPSBgPCR7Y2hpbGQudHlwZX0vPmA7XG4gICAgICB9IGVsc2UgaWYgKCFjaGlsZENoaWxkcmVuICYmICghc2hvdWxkS2VlcENoaWxkIHx8IGNoaWxkUHJvcHNDb3VudCAhPT0gMCkpIHtcbiAgICAgICAgc3RyaW5nTm9kZSArPSBgPCR7Y2hpbGRJbmRleH0+PC8ke2NoaWxkSW5kZXh9PmA7XG4gICAgICB9IGVsc2UgaWYgKGNoaWxkLnByb3BzLmkxOG5Jc0R5bmFtaWNMaXN0KSB7XG4gICAgICAgIHN0cmluZ05vZGUgKz0gYDwke2NoaWxkSW5kZXh9PjwvJHtjaGlsZEluZGV4fT5gO1xuICAgICAgfSBlbHNlIGlmIChzaG91bGRLZWVwQ2hpbGQgJiYgY2hpbGRQcm9wc0NvdW50ID09PSAxICYmIHR5cGVvZiBjaGlsZENoaWxkcmVuID09PSAnc3RyaW5nJykge1xuICAgICAgICBzdHJpbmdOb2RlICs9IGA8JHtjaGlsZC50eXBlfT4ke2NoaWxkQ2hpbGRyZW59PC8ke2NoaWxkLnR5cGV9PmA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBjb250ZW50ID0gbm9kZXNUb1N0cmluZyhjaGlsZENoaWxkcmVuLCBpMThuT3B0aW9ucyk7XG4gICAgICAgIHN0cmluZ05vZGUgKz0gYDwke2NoaWxkSW5kZXh9PiR7Y29udGVudH08LyR7Y2hpbGRJbmRleH0+YDtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKGNoaWxkID09PSBudWxsKSB7XG4gICAgICB3YXJuKGBUcmFuczogdGhlIHBhc3NlZCBpbiB2YWx1ZSBpcyBpbnZhbGlkIC0gc2VlbXMgeW91IHBhc3NlZCBpbiBhIG51bGwgY2hpbGQuYCk7XG4gICAgfSBlbHNlIGlmICh0eXBlb2YgY2hpbGQgPT09ICdvYmplY3QnKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIGZvcm1hdCxcbiAgICAgICAgLi4uY2xvbmVcbiAgICAgIH0gPSBjaGlsZDtcbiAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyhjbG9uZSk7XG4gICAgICBpZiAoa2V5cy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBmb3JtYXQgPyBgJHtrZXlzWzBdfSwgJHtmb3JtYXR9YCA6IGtleXNbMF07XG4gICAgICAgIHN0cmluZ05vZGUgKz0gYHt7JHt2YWx1ZX19fWA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB3YXJuKGByZWFjdC1pMThuZXh0OiB0aGUgcGFzc2VkIGluIG9iamVjdCBjb250YWluZWQgbW9yZSB0aGFuIG9uZSB2YXJpYWJsZSAtIHRoZSBvYmplY3Qgc2hvdWxkIGxvb2sgbGlrZSB7eyB2YWx1ZSwgZm9ybWF0IH19IHdoZXJlIGZvcm1hdCBpcyBvcHRpb25hbC5gLCBjaGlsZCk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHdhcm4oYFRyYW5zOiB0aGUgcGFzc2VkIGluIHZhbHVlIGlzIGludmFsaWQgLSBzZWVtcyB5b3UgcGFzc2VkIGluIGEgdmFyaWFibGUgbGlrZSB7bnVtYmVyfSAtIHBsZWFzZSBwYXNzIGluIHZhcmlhYmxlcyBmb3IgaW50ZXJwb2xhdGlvbiBhcyBmdWxsIG9iamVjdHMgbGlrZSB7e251bWJlcn19LmAsIGNoaWxkKTtcbiAgICB9XG4gIH0pO1xuICByZXR1cm4gc3RyaW5nTm9kZTtcbn1cbmZ1bmN0aW9uIHJlbmRlck5vZGVzKGNoaWxkcmVuLCB0YXJnZXRTdHJpbmcsIGkxOG4sIGkxOG5PcHRpb25zLCBjb21iaW5lZFRPcHRzLCBzaG91bGRVbmVzY2FwZSkge1xuICBpZiAodGFyZ2V0U3RyaW5nID09PSAnJykgcmV0dXJuIFtdO1xuICBjb25zdCBrZWVwQXJyYXkgPSBpMThuT3B0aW9ucy50cmFuc0tlZXBCYXNpY0h0bWxOb2Rlc0ZvciB8fCBbXTtcbiAgY29uc3QgZW1wdHlDaGlsZHJlbkJ1dE5lZWRzSGFuZGxpbmcgPSB0YXJnZXRTdHJpbmcgJiYgbmV3IFJlZ0V4cChrZWVwQXJyYXkubWFwKGtlZXAgPT4gYDwke2tlZXB9YCkuam9pbignfCcpKS50ZXN0KHRhcmdldFN0cmluZyk7XG4gIGlmICghY2hpbGRyZW4gJiYgIWVtcHR5Q2hpbGRyZW5CdXROZWVkc0hhbmRsaW5nICYmICFzaG91bGRVbmVzY2FwZSkgcmV0dXJuIFt0YXJnZXRTdHJpbmddO1xuICBjb25zdCBkYXRhID0ge307XG4gIGZ1bmN0aW9uIGdldERhdGEoY2hpbGRzKSB7XG4gICAgY29uc3QgY2hpbGRyZW5BcnJheSA9IGdldEFzQXJyYXkoY2hpbGRzKTtcbiAgICBjaGlsZHJlbkFycmF5LmZvckVhY2goY2hpbGQgPT4ge1xuICAgICAgaWYgKHR5cGVvZiBjaGlsZCA9PT0gJ3N0cmluZycpIHJldHVybjtcbiAgICAgIGlmIChoYXNDaGlsZHJlbihjaGlsZCkpIGdldERhdGEoZ2V0Q2hpbGRyZW4oY2hpbGQpKTtlbHNlIGlmICh0eXBlb2YgY2hpbGQgPT09ICdvYmplY3QnICYmICFpc1ZhbGlkRWxlbWVudChjaGlsZCkpIE9iamVjdC5hc3NpZ24oZGF0YSwgY2hpbGQpO1xuICAgIH0pO1xuICB9XG4gIGdldERhdGEoY2hpbGRyZW4pO1xuICBjb25zdCBhc3QgPSBIVE1MLnBhcnNlKGA8MD4ke3RhcmdldFN0cmluZ308LzA+YCk7XG4gIGNvbnN0IG9wdHMgPSB7XG4gICAgLi4uZGF0YSxcbiAgICAuLi5jb21iaW5lZFRPcHRzXG4gIH07XG4gIGZ1bmN0aW9uIHJlbmRlcklubmVyKGNoaWxkLCBub2RlLCByb290UmVhY3ROb2RlKSB7XG4gICAgY29uc3QgY2hpbGRzID0gZ2V0Q2hpbGRyZW4oY2hpbGQpO1xuICAgIGNvbnN0IG1hcHBlZENoaWxkcmVuID0gbWFwQVNUKGNoaWxkcywgbm9kZS5jaGlsZHJlbiwgcm9vdFJlYWN0Tm9kZSk7XG4gICAgcmV0dXJuIGhhc1ZhbGlkUmVhY3RDaGlsZHJlbihjaGlsZHMpICYmIG1hcHBlZENoaWxkcmVuLmxlbmd0aCA9PT0gMCB8fCBjaGlsZC5wcm9wcyAmJiBjaGlsZC5wcm9wcy5pMThuSXNEeW5hbWljTGlzdCA/IGNoaWxkcyA6IG1hcHBlZENoaWxkcmVuO1xuICB9XG4gIGZ1bmN0aW9uIHB1c2hUcmFuc2xhdGVkSlNYKGNoaWxkLCBpbm5lciwgbWVtLCBpLCBpc1ZvaWQpIHtcbiAgICBpZiAoY2hpbGQuZHVtbXkpIHtcbiAgICAgIGNoaWxkLmNoaWxkcmVuID0gaW5uZXI7XG4gICAgICBtZW0ucHVzaChjbG9uZUVsZW1lbnQoY2hpbGQsIHtcbiAgICAgICAga2V5OiBpXG4gICAgICB9LCBpc1ZvaWQgPyB1bmRlZmluZWQgOiBpbm5lcikpO1xuICAgIH0gZWxzZSB7XG4gICAgICBtZW0ucHVzaCguLi5DaGlsZHJlbi5tYXAoW2NoaWxkXSwgYyA9PiB7XG4gICAgICAgIGNvbnN0IHByb3BzID0ge1xuICAgICAgICAgIC4uLmMucHJvcHNcbiAgICAgICAgfTtcbiAgICAgICAgZGVsZXRlIHByb3BzLmkxOG5Jc0R5bmFtaWNMaXN0O1xuICAgICAgICByZXR1cm4gY3JlYXRlRWxlbWVudChjLnR5cGUsIHtcbiAgICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgICBrZXk6IGksXG4gICAgICAgICAgcmVmOiBjLnJlZlxuICAgICAgICB9LCBpc1ZvaWQgPyBudWxsIDogaW5uZXIpO1xuICAgICAgfSkpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBtYXBBU1QocmVhY3ROb2RlLCBhc3ROb2RlLCByb290UmVhY3ROb2RlKSB7XG4gICAgY29uc3QgcmVhY3ROb2RlcyA9IGdldEFzQXJyYXkocmVhY3ROb2RlKTtcbiAgICBjb25zdCBhc3ROb2RlcyA9IGdldEFzQXJyYXkoYXN0Tm9kZSk7XG4gICAgcmV0dXJuIGFzdE5vZGVzLnJlZHVjZSgobWVtLCBub2RlLCBpKSA9PiB7XG4gICAgICBjb25zdCB0cmFuc2xhdGlvbkNvbnRlbnQgPSBub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW5bMF0gJiYgbm9kZS5jaGlsZHJlblswXS5jb250ZW50ICYmIGkxOG4uc2VydmljZXMuaW50ZXJwb2xhdG9yLmludGVycG9sYXRlKG5vZGUuY2hpbGRyZW5bMF0uY29udGVudCwgb3B0cywgaTE4bi5sYW5ndWFnZSk7XG4gICAgICBpZiAobm9kZS50eXBlID09PSAndGFnJykge1xuICAgICAgICBsZXQgdG1wID0gcmVhY3ROb2Rlc1twYXJzZUludChub2RlLm5hbWUsIDEwKV07XG4gICAgICAgIGlmIChyb290UmVhY3ROb2RlLmxlbmd0aCA9PT0gMSAmJiAhdG1wKSB0bXAgPSByb290UmVhY3ROb2RlWzBdW25vZGUubmFtZV07XG4gICAgICAgIGlmICghdG1wKSB0bXAgPSB7fTtcbiAgICAgICAgY29uc3QgY2hpbGQgPSBPYmplY3Qua2V5cyhub2RlLmF0dHJzKS5sZW5ndGggIT09IDAgPyBtZXJnZVByb3BzKHtcbiAgICAgICAgICBwcm9wczogbm9kZS5hdHRyc1xuICAgICAgICB9LCB0bXApIDogdG1wO1xuICAgICAgICBjb25zdCBpc0VsZW1lbnQgPSBpc1ZhbGlkRWxlbWVudChjaGlsZCk7XG4gICAgICAgIGNvbnN0IGlzVmFsaWRUcmFuc2xhdGlvbldpdGhDaGlsZHJlbiA9IGlzRWxlbWVudCAmJiBoYXNDaGlsZHJlbihub2RlLCB0cnVlKSAmJiAhbm9kZS52b2lkRWxlbWVudDtcbiAgICAgICAgY29uc3QgaXNFbXB0eVRyYW5zV2l0aEhUTUwgPSBlbXB0eUNoaWxkcmVuQnV0TmVlZHNIYW5kbGluZyAmJiB0eXBlb2YgY2hpbGQgPT09ICdvYmplY3QnICYmIGNoaWxkLmR1bW15ICYmICFpc0VsZW1lbnQ7XG4gICAgICAgIGNvbnN0IGlzS25vd25Db21wb25lbnQgPSB0eXBlb2YgY2hpbGRyZW4gPT09ICdvYmplY3QnICYmIGNoaWxkcmVuICE9PSBudWxsICYmIE9iamVjdC5oYXNPd25Qcm9wZXJ0eS5jYWxsKGNoaWxkcmVuLCBub2RlLm5hbWUpO1xuICAgICAgICBpZiAodHlwZW9mIGNoaWxkID09PSAnc3RyaW5nJykge1xuICAgICAgICAgIGNvbnN0IHZhbHVlID0gaTE4bi5zZXJ2aWNlcy5pbnRlcnBvbGF0b3IuaW50ZXJwb2xhdGUoY2hpbGQsIG9wdHMsIGkxOG4ubGFuZ3VhZ2UpO1xuICAgICAgICAgIG1lbS5wdXNoKHZhbHVlKTtcbiAgICAgICAgfSBlbHNlIGlmIChoYXNDaGlsZHJlbihjaGlsZCkgfHwgaXNWYWxpZFRyYW5zbGF0aW9uV2l0aENoaWxkcmVuKSB7XG4gICAgICAgICAgY29uc3QgaW5uZXIgPSByZW5kZXJJbm5lcihjaGlsZCwgbm9kZSwgcm9vdFJlYWN0Tm9kZSk7XG4gICAgICAgICAgcHVzaFRyYW5zbGF0ZWRKU1goY2hpbGQsIGlubmVyLCBtZW0sIGkpO1xuICAgICAgICB9IGVsc2UgaWYgKGlzRW1wdHlUcmFuc1dpdGhIVE1MKSB7XG4gICAgICAgICAgY29uc3QgaW5uZXIgPSBtYXBBU1QocmVhY3ROb2Rlcywgbm9kZS5jaGlsZHJlbiwgcm9vdFJlYWN0Tm9kZSk7XG4gICAgICAgICAgcHVzaFRyYW5zbGF0ZWRKU1goY2hpbGQsIGlubmVyLCBtZW0sIGkpO1xuICAgICAgICB9IGVsc2UgaWYgKE51bWJlci5pc05hTihwYXJzZUZsb2F0KG5vZGUubmFtZSkpKSB7XG4gICAgICAgICAgaWYgKGlzS25vd25Db21wb25lbnQpIHtcbiAgICAgICAgICAgIGNvbnN0IGlubmVyID0gcmVuZGVySW5uZXIoY2hpbGQsIG5vZGUsIHJvb3RSZWFjdE5vZGUpO1xuICAgICAgICAgICAgcHVzaFRyYW5zbGF0ZWRKU1goY2hpbGQsIGlubmVyLCBtZW0sIGksIG5vZGUudm9pZEVsZW1lbnQpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoaTE4bk9wdGlvbnMudHJhbnNTdXBwb3J0QmFzaWNIdG1sTm9kZXMgJiYga2VlcEFycmF5LmluZGV4T2Yobm9kZS5uYW1lKSA+IC0xKSB7XG4gICAgICAgICAgICBpZiAobm9kZS52b2lkRWxlbWVudCkge1xuICAgICAgICAgICAgICBtZW0ucHVzaChjcmVhdGVFbGVtZW50KG5vZGUubmFtZSwge1xuICAgICAgICAgICAgICAgIGtleTogYCR7bm9kZS5uYW1lfS0ke2l9YFxuICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBjb25zdCBpbm5lciA9IG1hcEFTVChyZWFjdE5vZGVzLCBub2RlLmNoaWxkcmVuLCByb290UmVhY3ROb2RlKTtcbiAgICAgICAgICAgICAgbWVtLnB1c2goY3JlYXRlRWxlbWVudChub2RlLm5hbWUsIHtcbiAgICAgICAgICAgICAgICBrZXk6IGAke25vZGUubmFtZX0tJHtpfWBcbiAgICAgICAgICAgICAgfSwgaW5uZXIpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUudm9pZEVsZW1lbnQpIHtcbiAgICAgICAgICAgIG1lbS5wdXNoKGA8JHtub2RlLm5hbWV9IC8+YCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IGlubmVyID0gbWFwQVNUKHJlYWN0Tm9kZXMsIG5vZGUuY2hpbGRyZW4sIHJvb3RSZWFjdE5vZGUpO1xuICAgICAgICAgICAgbWVtLnB1c2goYDwke25vZGUubmFtZX0+JHtpbm5lcn08LyR7bm9kZS5uYW1lfT5gKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIGNoaWxkID09PSAnb2JqZWN0JyAmJiAhaXNFbGVtZW50KSB7XG4gICAgICAgICAgY29uc3QgY29udGVudCA9IG5vZGUuY2hpbGRyZW5bMF0gPyB0cmFuc2xhdGlvbkNvbnRlbnQgOiBudWxsO1xuICAgICAgICAgIGlmIChjb250ZW50KSBtZW0ucHVzaChjb250ZW50KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBwdXNoVHJhbnNsYXRlZEpTWChjaGlsZCwgdHJhbnNsYXRpb25Db250ZW50LCBtZW0sIGksIG5vZGUuY2hpbGRyZW4ubGVuZ3RoICE9PSAxIHx8ICF0cmFuc2xhdGlvbkNvbnRlbnQpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKG5vZGUudHlwZSA9PT0gJ3RleHQnKSB7XG4gICAgICAgIGNvbnN0IHdyYXBUZXh0Tm9kZXMgPSBpMThuT3B0aW9ucy50cmFuc1dyYXBUZXh0Tm9kZXM7XG4gICAgICAgIGNvbnN0IGNvbnRlbnQgPSBzaG91bGRVbmVzY2FwZSA/IGkxOG5PcHRpb25zLnVuZXNjYXBlKGkxOG4uc2VydmljZXMuaW50ZXJwb2xhdG9yLmludGVycG9sYXRlKG5vZGUuY29udGVudCwgb3B0cywgaTE4bi5sYW5ndWFnZSkpIDogaTE4bi5zZXJ2aWNlcy5pbnRlcnBvbGF0b3IuaW50ZXJwb2xhdGUobm9kZS5jb250ZW50LCBvcHRzLCBpMThuLmxhbmd1YWdlKTtcbiAgICAgICAgaWYgKHdyYXBUZXh0Tm9kZXMpIHtcbiAgICAgICAgICBtZW0ucHVzaChjcmVhdGVFbGVtZW50KHdyYXBUZXh0Tm9kZXMsIHtcbiAgICAgICAgICAgIGtleTogYCR7bm9kZS5uYW1lfS0ke2l9YFxuICAgICAgICAgIH0sIGNvbnRlbnQpKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBtZW0ucHVzaChjb250ZW50KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIG1lbTtcbiAgICB9LCBbXSk7XG4gIH1cbiAgY29uc3QgcmVzdWx0ID0gbWFwQVNUKFt7XG4gICAgZHVtbXk6IHRydWUsXG4gICAgY2hpbGRyZW46IGNoaWxkcmVuIHx8IFtdXG4gIH1dLCBhc3QsIGdldEFzQXJyYXkoY2hpbGRyZW4gfHwgW10pKTtcbiAgcmV0dXJuIGdldENoaWxkcmVuKHJlc3VsdFswXSk7XG59XG5leHBvcnQgZnVuY3Rpb24gVHJhbnMoX3JlZikge1xuICBsZXQge1xuICAgIGNoaWxkcmVuLFxuICAgIGNvdW50LFxuICAgIHBhcmVudCxcbiAgICBpMThuS2V5LFxuICAgIGNvbnRleHQsXG4gICAgdE9wdGlvbnMgPSB7fSxcbiAgICB2YWx1ZXMsXG4gICAgZGVmYXVsdHMsXG4gICAgY29tcG9uZW50cyxcbiAgICBucyxcbiAgICBpMThuOiBpMThuRnJvbVByb3BzLFxuICAgIHQ6IHRGcm9tUHJvcHMsXG4gICAgc2hvdWxkVW5lc2NhcGUsXG4gICAgLi4uYWRkaXRpb25hbFByb3BzXG4gIH0gPSBfcmVmO1xuICBjb25zdCBpMThuID0gaTE4bkZyb21Qcm9wcyB8fCBnZXRJMThuKCk7XG4gIGlmICghaTE4bikge1xuICAgIHdhcm5PbmNlKCdZb3Ugd2lsbCBuZWVkIHRvIHBhc3MgaW4gYW4gaTE4bmV4dCBpbnN0YW5jZSBieSB1c2luZyBpMThuZXh0UmVhY3RNb2R1bGUnKTtcbiAgICByZXR1cm4gY2hpbGRyZW47XG4gIH1cbiAgY29uc3QgdCA9IHRGcm9tUHJvcHMgfHwgaTE4bi50LmJpbmQoaTE4bikgfHwgKGsgPT4gayk7XG4gIGlmIChjb250ZXh0KSB0T3B0aW9ucy5jb250ZXh0ID0gY29udGV4dDtcbiAgY29uc3QgcmVhY3RJMThuZXh0T3B0aW9ucyA9IHtcbiAgICAuLi5nZXREZWZhdWx0cygpLFxuICAgIC4uLihpMThuLm9wdGlvbnMgJiYgaTE4bi5vcHRpb25zLnJlYWN0KVxuICB9O1xuICBsZXQgbmFtZXNwYWNlcyA9IG5zIHx8IHQubnMgfHwgaTE4bi5vcHRpb25zICYmIGkxOG4ub3B0aW9ucy5kZWZhdWx0TlM7XG4gIG5hbWVzcGFjZXMgPSB0eXBlb2YgbmFtZXNwYWNlcyA9PT0gJ3N0cmluZycgPyBbbmFtZXNwYWNlc10gOiBuYW1lc3BhY2VzIHx8IFsndHJhbnNsYXRpb24nXTtcbiAgY29uc3Qgbm9kZUFzU3RyaW5nID0gbm9kZXNUb1N0cmluZyhjaGlsZHJlbiwgcmVhY3RJMThuZXh0T3B0aW9ucyk7XG4gIGNvbnN0IGRlZmF1bHRWYWx1ZSA9IGRlZmF1bHRzIHx8IG5vZGVBc1N0cmluZyB8fCByZWFjdEkxOG5leHRPcHRpb25zLnRyYW5zRW1wdHlOb2RlVmFsdWUgfHwgaTE4bktleTtcbiAgY29uc3Qge1xuICAgIGhhc2hUcmFuc0tleVxuICB9ID0gcmVhY3RJMThuZXh0T3B0aW9ucztcbiAgY29uc3Qga2V5ID0gaTE4bktleSB8fCAoaGFzaFRyYW5zS2V5ID8gaGFzaFRyYW5zS2V5KG5vZGVBc1N0cmluZyB8fCBkZWZhdWx0VmFsdWUpIDogbm9kZUFzU3RyaW5nIHx8IGRlZmF1bHRWYWx1ZSk7XG4gIGlmIChpMThuLm9wdGlvbnMgJiYgaTE4bi5vcHRpb25zLmludGVycG9sYXRpb24gJiYgaTE4bi5vcHRpb25zLmludGVycG9sYXRpb24uZGVmYXVsdFZhcmlhYmxlcykge1xuICAgIHZhbHVlcyA9IHZhbHVlcyAmJiBPYmplY3Qua2V5cyh2YWx1ZXMpLmxlbmd0aCA+IDAgPyB7XG4gICAgICAuLi52YWx1ZXMsXG4gICAgICAuLi5pMThuLm9wdGlvbnMuaW50ZXJwb2xhdGlvbi5kZWZhdWx0VmFyaWFibGVzXG4gICAgfSA6IHtcbiAgICAgIC4uLmkxOG4ub3B0aW9ucy5pbnRlcnBvbGF0aW9uLmRlZmF1bHRWYXJpYWJsZXNcbiAgICB9O1xuICB9XG4gIGNvbnN0IGludGVycG9sYXRpb25PdmVycmlkZSA9IHZhbHVlcyB8fCBjb3VudCAhPT0gdW5kZWZpbmVkID8gdE9wdGlvbnMuaW50ZXJwb2xhdGlvbiA6IHtcbiAgICBpbnRlcnBvbGF0aW9uOiB7XG4gICAgICAuLi50T3B0aW9ucy5pbnRlcnBvbGF0aW9uLFxuICAgICAgcHJlZml4OiAnIyQ/JyxcbiAgICAgIHN1ZmZpeDogJz8kIydcbiAgICB9XG4gIH07XG4gIGNvbnN0IGNvbWJpbmVkVE9wdHMgPSB7XG4gICAgLi4udE9wdGlvbnMsXG4gICAgY291bnQsXG4gICAgLi4udmFsdWVzLFxuICAgIC4uLmludGVycG9sYXRpb25PdmVycmlkZSxcbiAgICBkZWZhdWx0VmFsdWUsXG4gICAgbnM6IG5hbWVzcGFjZXNcbiAgfTtcbiAgY29uc3QgdHJhbnNsYXRpb24gPSBrZXkgPyB0KGtleSwgY29tYmluZWRUT3B0cykgOiBkZWZhdWx0VmFsdWU7XG4gIGlmIChjb21wb25lbnRzKSB7XG4gICAgT2JqZWN0LmtleXMoY29tcG9uZW50cykuZm9yRWFjaChjID0+IHtcbiAgICAgIGNvbnN0IGNvbXAgPSBjb21wb25lbnRzW2NdO1xuICAgICAgaWYgKHR5cGVvZiBjb21wLnR5cGUgPT09ICdmdW5jdGlvbicgfHwgIWNvbXAucHJvcHMgfHwgIWNvbXAucHJvcHMuY2hpbGRyZW4gfHwgdHJhbnNsYXRpb24uaW5kZXhPZihgJHtjfS8+YCkgPCAwICYmIHRyYW5zbGF0aW9uLmluZGV4T2YoYCR7Y30gLz5gKSA8IDApIHJldHVybjtcbiAgICAgIGZ1bmN0aW9uIENvbXBvbmVudGl6ZWQoKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVFbGVtZW50KEZyYWdtZW50LCBudWxsLCBjb21wKTtcbiAgICAgIH1cbiAgICAgIGNvbXBvbmVudHNbY10gPSBjcmVhdGVFbGVtZW50KENvbXBvbmVudGl6ZWQpO1xuICAgIH0pO1xuICB9XG4gIGNvbnN0IGNvbnRlbnQgPSByZW5kZXJOb2Rlcyhjb21wb25lbnRzIHx8IGNoaWxkcmVuLCB0cmFuc2xhdGlvbiwgaTE4biwgcmVhY3RJMThuZXh0T3B0aW9ucywgY29tYmluZWRUT3B0cywgc2hvdWxkVW5lc2NhcGUpO1xuICBjb25zdCB1c2VBc1BhcmVudCA9IHBhcmVudCAhPT0gdW5kZWZpbmVkID8gcGFyZW50IDogcmVhY3RJMThuZXh0T3B0aW9ucy5kZWZhdWx0VHJhbnNQYXJlbnQ7XG4gIHJldHVybiB1c2VBc1BhcmVudCA/IGNyZWF0ZUVsZW1lbnQodXNlQXNQYXJlbnQsIGFkZGl0aW9uYWxQcm9wcywgY29udGVudCkgOiBjb250ZW50O1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/Translation.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Translation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translation: () => (/* binding */ Translation)\n/* harmony export */ });\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n\nfunction Translation(props) {\n  const {\n    ns,\n    children,\n    ...options\n  } = props;\n  const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_0__.useTranslation)(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL1RyYW5zbGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQzlDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0osMkJBQTJCLGtFQUFjO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWkxOG5leHQvZGlzdC9lcy9UcmFuc2xhdGlvbi5qcz9jMDU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnLi91c2VUcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgZnVuY3Rpb24gVHJhbnNsYXRpb24ocHJvcHMpIHtcbiAgY29uc3Qge1xuICAgIG5zLFxuICAgIGNoaWxkcmVuLFxuICAgIC4uLm9wdGlvbnNcbiAgfSA9IHByb3BzO1xuICBjb25zdCBbdCwgaTE4biwgcmVhZHldID0gdXNlVHJhbnNsYXRpb24obnMsIG9wdGlvbnMpO1xuICByZXR1cm4gY2hpbGRyZW4odCwge1xuICAgIGkxOG4sXG4gICAgbG5nOiBpMThuLmxhbmd1YWdlXG4gIH0sIHJlYWR5KTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/Translation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/context.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/context.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* binding */ I18nContext),\n/* harmony export */   ReportNamespaces: () => (/* binding */ ReportNamespaces),\n/* harmony export */   composeInitialProps: () => (/* binding */ composeInitialProps),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n),\n/* harmony export */   getInitialProps: () => (/* binding */ getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__.initReactI18next),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.setI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initReactI18next.js */ \"(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n\n\n\n\n\nconst I18nContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();\nclass ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nfunction composeInitialProps(ForComponent) {\n  return ctx => new Promise(resolve => {\n    const i18nInitialProps = getInitialProps();\n    if (ForComponent.getInitialProps) {\n      ForComponent.getInitialProps(ctx).then(componentsInitialProps => {\n        resolve({\n          ...componentsInitialProps,\n          ...i18nInitialProps\n        });\n      });\n    } else {\n      resolve(i18nInitialProps);\n    }\n  });\n}\nfunction getInitialProps() {\n  const i18n = (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const namespaces = i18n.reportNamespaces ? i18n.reportNamespaces.getUsedNamespaces() : [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/defaults.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/defaults.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   setDefaults: () => (/* binding */ setDefaults)\n/* harmony export */ });\n/* harmony import */ var _unescape_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unescape.js */ \"(ssr)/./node_modules/react-i18next/dist/es/unescape.js\");\n\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape: _unescape_js__WEBPACK_IMPORTED_MODULE_0__.unescape\n};\nfunction setDefaults() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n}\nfunction getDefaults() {\n  return defaultOptions;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcnlzdGFsYWxpZ25lci8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvZGVmYXVsdHMuanM/ZjVlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1bmVzY2FwZSB9IGZyb20gJy4vdW5lc2NhcGUuanMnO1xubGV0IGRlZmF1bHRPcHRpb25zID0ge1xuICBiaW5kSTE4bjogJ2xhbmd1YWdlQ2hhbmdlZCcsXG4gIGJpbmRJMThuU3RvcmU6ICcnLFxuICB0cmFuc0VtcHR5Tm9kZVZhbHVlOiAnJyxcbiAgdHJhbnNTdXBwb3J0QmFzaWNIdG1sTm9kZXM6IHRydWUsXG4gIHRyYW5zV3JhcFRleHROb2RlczogJycsXG4gIHRyYW5zS2VlcEJhc2ljSHRtbE5vZGVzRm9yOiBbJ2JyJywgJ3N0cm9uZycsICdpJywgJ3AnXSxcbiAgdXNlU3VzcGVuc2U6IHRydWUsXG4gIHVuZXNjYXBlXG59O1xuZXhwb3J0IGZ1bmN0aW9uIHNldERlZmF1bHRzKCkge1xuICBsZXQgb3B0aW9ucyA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDoge307XG4gIGRlZmF1bHRPcHRpb25zID0ge1xuICAgIC4uLmRlZmF1bHRPcHRpb25zLFxuICAgIC4uLm9wdGlvbnNcbiAgfTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXREZWZhdWx0cygpIHtcbiAgcmV0dXJuIGRlZmF1bHRPcHRpb25zO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js":
/*!************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/i18nInstance.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   setI18n: () => (/* binding */ setI18n)\n/* harmony export */ });\nlet i18nInstance;\nfunction setI18n(instance) {\n  i18nInstance = instance;\n}\nfunction getI18n() {\n  return i18nInstance;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2kxOG5JbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2kxOG5JbnN0YW5jZS5qcz85MTM2Il0sInNvdXJjZXNDb250ZW50IjpbImxldCBpMThuSW5zdGFuY2U7XG5leHBvcnQgZnVuY3Rpb24gc2V0STE4bihpbnN0YW5jZSkge1xuICBpMThuSW5zdGFuY2UgPSBpbnN0YW5jZTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRJMThuKCkge1xuICByZXR1cm4gaTE4bkluc3RhbmNlO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.I18nContext),\n/* harmony export */   I18nextProvider: () => (/* reexport safe */ _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__.I18nextProvider),\n/* harmony export */   Trans: () => (/* reexport safe */ _Trans_js__WEBPACK_IMPORTED_MODULE_0__.Trans),\n/* harmony export */   TransWithoutContext: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans),\n/* harmony export */   Translation: () => (/* reexport safe */ _Translation_js__WEBPACK_IMPORTED_MODULE_4__.Translation),\n/* harmony export */   composeInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.composeInitialProps),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.getI18n),\n/* harmony export */   getInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__.initReactI18next),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   plural: () => (/* binding */ plural),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectOrdinal: () => (/* binding */ selectOrdinal),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.setI18n),\n/* harmony export */   time: () => (/* binding */ time),\n/* harmony export */   useSSR: () => (/* reexport safe */ _useSSR_js__WEBPACK_IMPORTED_MODULE_7__.useSSR),\n/* harmony export */   useTranslation: () => (/* reexport safe */ _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__.useTranslation),\n/* harmony export */   withSSR: () => (/* reexport safe */ _withSSR_js__WEBPACK_IMPORTED_MODULE_6__.withSSR),\n/* harmony export */   withTranslation: () => (/* reexport safe */ _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__.withTranslation)\n/* harmony export */ });\n/* harmony import */ var _Trans_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Trans.js */ \"(ssr)/./node_modules/react-i18next/dist/es/Trans.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(ssr)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./withTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js\");\n/* harmony import */ var _Translation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Translation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/Translation.js\");\n/* harmony import */ var _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./I18nextProvider.js */ \"(ssr)/./node_modules/react-i18next/dist/es/I18nextProvider.js\");\n/* harmony import */ var _withSSR_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./withSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/withSSR.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./initReactI18next.js */ \"(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst date = () => '';\nconst time = () => '';\nconst number = () => '';\nconst select = () => '';\nconst plural = () => '';\nconst selectOrdinal = () => '';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUM7QUFDcUM7QUFDbkI7QUFDRTtBQUNSO0FBQ1E7QUFDaEI7QUFDRjtBQUNvQjtBQUNBO0FBQ0o7QUFDNEI7QUFDMUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luZGV4LmpzPzM0ZWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgVHJhbnMgfSBmcm9tICcuL1RyYW5zLmpzJztcbmV4cG9ydCB7IFRyYW5zIGFzIFRyYW5zV2l0aG91dENvbnRleHQgfSBmcm9tICcuL1RyYW5zV2l0aG91dENvbnRleHQuanMnO1xuZXhwb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICcuL3VzZVRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IHdpdGhUcmFuc2xhdGlvbiB9IGZyb20gJy4vd2l0aFRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IFRyYW5zbGF0aW9uIH0gZnJvbSAnLi9UcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICcuL0kxOG5leHRQcm92aWRlci5qcyc7XG5leHBvcnQgeyB3aXRoU1NSIH0gZnJvbSAnLi93aXRoU1NSLmpzJztcbmV4cG9ydCB7IHVzZVNTUiB9IGZyb20gJy4vdXNlU1NSLmpzJztcbmV4cG9ydCB7IGluaXRSZWFjdEkxOG5leHQgfSBmcm9tICcuL2luaXRSZWFjdEkxOG5leHQuanMnO1xuZXhwb3J0IHsgc2V0RGVmYXVsdHMsIGdldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5leHBvcnQgeyBzZXRJMThuLCBnZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuZXhwb3J0IHsgSTE4bkNvbnRleHQsIGNvbXBvc2VJbml0aWFsUHJvcHMsIGdldEluaXRpYWxQcm9wcyB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5leHBvcnQgY29uc3QgZGF0ZSA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IHRpbWUgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBudW1iZXIgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBzZWxlY3QgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBwbHVyYWwgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBzZWxlY3RPcmRpbmFsID0gKCkgPT4gJyc7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/initReactI18next.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initReactI18next: () => (/* binding */ initReactI18next)\n/* harmony export */ });\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.js */ \"(ssr)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18nInstance.js */ \"(ssr)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\nconst initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    (0,_defaults_js__WEBPACK_IMPORTED_MODULE_0__.setDefaults)(instance.options.react);\n    (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__.setI18n)(instance);\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luaXRSZWFjdEkxOG5leHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0E7QUFDckM7QUFDUDtBQUNBO0FBQ0EsSUFBSSx5REFBVztBQUNmLElBQUkseURBQU87QUFDWDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3J5c3RhbGFsaWduZXIvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL2luaXRSZWFjdEkxOG5leHQuanM/ZjU5NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzZXREZWZhdWx0cyB9IGZyb20gJy4vZGVmYXVsdHMuanMnO1xuaW1wb3J0IHsgc2V0STE4biB9IGZyb20gJy4vaTE4bkluc3RhbmNlLmpzJztcbmV4cG9ydCBjb25zdCBpbml0UmVhY3RJMThuZXh0ID0ge1xuICB0eXBlOiAnM3JkUGFydHknLFxuICBpbml0KGluc3RhbmNlKSB7XG4gICAgc2V0RGVmYXVsdHMoaW5zdGFuY2Uub3B0aW9ucy5yZWFjdCk7XG4gICAgc2V0STE4bihpbnN0YW5jZSk7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/initReactI18next.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/unescape.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/unescape.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unescape: () => (/* binding */ unescape)\n/* harmony export */ });\nconst matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nconst unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VuZXNjYXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx5SEFBeUg7QUFDekg7QUFDQSxRQUFRO0FBQ1IsUUFBUTtBQUNSLE9BQU87QUFDUCxRQUFRO0FBQ1IsT0FBTztBQUNQLFFBQVE7QUFDUixTQUFTO0FBQ1QsUUFBUTtBQUNSLFNBQVM7QUFDVCxRQUFRO0FBQ1IsU0FBUztBQUNULFNBQVM7QUFDVCxTQUFTO0FBQ1QsU0FBUztBQUNULFFBQVE7QUFDUixTQUFTO0FBQ1QsV0FBVztBQUNYLFVBQVU7QUFDVixTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWkxOG5leHQvZGlzdC9lcy91bmVzY2FwZS5qcz9jMjc0Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1hdGNoSHRtbEVudGl0eSA9IC8mKD86YW1wfCMzOHxsdHwjNjB8Z3R8IzYyfGFwb3N8IzM5fHF1b3R8IzM0fG5ic3B8IzE2MHxjb3B5fCMxNjl8cmVnfCMxNzR8aGVsbGlwfCM4MjMwfCN4MkZ8IzQ3KTsvZztcbmNvbnN0IGh0bWxFbnRpdGllcyA9IHtcbiAgJyZhbXA7JzogJyYnLFxuICAnJiMzODsnOiAnJicsXG4gICcmbHQ7JzogJzwnLFxuICAnJiM2MDsnOiAnPCcsXG4gICcmZ3Q7JzogJz4nLFxuICAnJiM2MjsnOiAnPicsXG4gICcmYXBvczsnOiBcIidcIixcbiAgJyYjMzk7JzogXCInXCIsXG4gICcmcXVvdDsnOiAnXCInLFxuICAnJiMzNDsnOiAnXCInLFxuICAnJm5ic3A7JzogJyAnLFxuICAnJiMxNjA7JzogJyAnLFxuICAnJmNvcHk7JzogJ8KpJyxcbiAgJyYjMTY5Oyc6ICfCqScsXG4gICcmcmVnOyc6ICfCricsXG4gICcmIzE3NDsnOiAnwq4nLFxuICAnJmhlbGxpcDsnOiAn4oCmJyxcbiAgJyYjODIzMDsnOiAn4oCmJyxcbiAgJyYjeDJGOyc6ICcvJyxcbiAgJyYjNDc7JzogJy8nXG59O1xuY29uc3QgdW5lc2NhcGVIdG1sRW50aXR5ID0gbSA9PiBodG1sRW50aXRpZXNbbV07XG5leHBvcnQgY29uc3QgdW5lc2NhcGUgPSB0ZXh0ID0+IHRleHQucmVwbGFjZShtYXRjaEh0bWxFbnRpdHksIHVuZXNjYXBlSHRtbEVudGl0eSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/unescape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/useSSR.js":
/*!******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useSSR.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSSR: () => (/* binding */ useSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nfunction useSSR(initialI18nStore, initialLanguage) {\n  let props = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n.options && i18n.options.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3VzZVNTUi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDaUI7QUFDN0M7QUFDUDtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUksRUFBRSxpREFBVSxDQUFDLG9EQUFXO0FBQzVCLG1EQUFtRCxvREFBTztBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vbm9kZV9tb2R1bGVzL3JlYWN0LWkxOG5leHQvZGlzdC9lcy91c2VTU1IuanM/NDI1OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0STE4biwgSTE4bkNvbnRleHQgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHVzZVNTUihpbml0aWFsSTE4blN0b3JlLCBpbml0aWFsTGFuZ3VhZ2UpIHtcbiAgbGV0IHByb3BzID0gYXJndW1lbnRzLmxlbmd0aCA+IDIgJiYgYXJndW1lbnRzWzJdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMl0gOiB7fTtcbiAgY29uc3Qge1xuICAgIGkxOG46IGkxOG5Gcm9tUHJvcHNcbiAgfSA9IHByb3BzO1xuICBjb25zdCB7XG4gICAgaTE4bjogaTE4bkZyb21Db250ZXh0XG4gIH0gPSB1c2VDb250ZXh0KEkxOG5Db250ZXh0KSB8fCB7fTtcbiAgY29uc3QgaTE4biA9IGkxOG5Gcm9tUHJvcHMgfHwgaTE4bkZyb21Db250ZXh0IHx8IGdldEkxOG4oKTtcbiAgaWYgKGkxOG4ub3B0aW9ucyAmJiBpMThuLm9wdGlvbnMuaXNDbG9uZSkgcmV0dXJuO1xuICBpZiAoaW5pdGlhbEkxOG5TdG9yZSAmJiAhaTE4bi5pbml0aWFsaXplZFN0b3JlT25jZSkge1xuICAgIGkxOG4uc2VydmljZXMucmVzb3VyY2VTdG9yZS5kYXRhID0gaW5pdGlhbEkxOG5TdG9yZTtcbiAgICBpMThuLm9wdGlvbnMubnMgPSBPYmplY3QudmFsdWVzKGluaXRpYWxJMThuU3RvcmUpLnJlZHVjZSgobWVtLCBsbmdSZXNvdXJjZXMpID0+IHtcbiAgICAgIE9iamVjdC5rZXlzKGxuZ1Jlc291cmNlcykuZm9yRWFjaChucyA9PiB7XG4gICAgICAgIGlmIChtZW0uaW5kZXhPZihucykgPCAwKSBtZW0ucHVzaChucyk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBtZW07XG4gICAgfSwgaTE4bi5vcHRpb25zLm5zKTtcbiAgICBpMThuLmluaXRpYWxpemVkU3RvcmVPbmNlID0gdHJ1ZTtcbiAgICBpMThuLmlzSW5pdGlhbGl6ZWQgPSB0cnVlO1xuICB9XG4gIGlmIChpbml0aWFsTGFuZ3VhZ2UgJiYgIWkxOG4uaW5pdGlhbGl6ZWRMYW5ndWFnZU9uY2UpIHtcbiAgICBpMThuLmNoYW5nZUxhbmd1YWdlKGluaXRpYWxMYW5ndWFnZSk7XG4gICAgaTE4bi5pbml0aWFsaXplZExhbmd1YWdlT25jZSA9IHRydWU7XG4gIH1cbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useTranslation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst usePrevious = (value, ignore) => {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nfunction alwaysNewT(i18n, language, namespace, keyPrefix) {\n  return i18n.getFixedT(language, namespace, keyPrefix);\n}\nfunction useMemoizedT(i18n, language, namespace, keyPrefix) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\n}\nfunction useTranslation(ns) {\n  let props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new _context_js__WEBPACK_IMPORTED_MODULE_1__.ReportNamespaces();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)('You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (typeof optsOrDefaultValue === 'string') return optsOrDefaultValue;\n      if (optsOrDefaultValue && typeof optsOrDefaultValue === 'object' && typeof optsOrDefaultValue.defaultValue === 'string') return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react && i18n.options.react.wait !== undefined) (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)('It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...(0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults)(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  if (i18n.reportNamespaces.addUsedNamespaces) i18n.reportNamespaces.addUsedNamespaces(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasLoadedNamespace)(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    function boundReset() {\n      if (isMounted.current) setT(getNewT);\n    }\n    if (bindI18n && i18n) i18n.on(bindI18n, boundReset);\n    if (bindI18nStore && i18n) i18n.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (bindI18n && i18n) bindI18n.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  const isInitial = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isMounted.current && !isInitial.current) {\n      setT(getT);\n    }\n    isInitial.current = false;\n  }, [i18n, keyPrefix]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => resolve());\n    }\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   hasLoadedNamespace: () => (/* binding */ hasLoadedNamespace),\n/* harmony export */   loadLanguages: () => (/* binding */ loadLanguages),\n/* harmony export */   loadNamespaces: () => (/* binding */ loadNamespaces),\n/* harmony export */   warn: () => (/* binding */ warn),\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nfunction warn() {\n  if (console && console.warn) {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (typeof args[0] === 'string') args[0] = `react-i18next:: ${args[0]}`;\n    console.warn(...args);\n  }\n}\nconst alreadyWarned = {};\nfunction warnOnce() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  if (typeof args[0] === 'string' && alreadyWarned[args[0]]) return;\n  if (typeof args[0] === 'string') alreadyWarned[args[0]] = new Date();\n  warn(...args);\n}\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nfunction loadNamespaces(i18n, ns, cb) {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n}\nfunction loadLanguages(i18n, lng, ns, cb) {\n  if (typeof ns === 'string') ns = [ns];\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n}\nfunction oldI18nextHasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const lng = i18n.languages[0];\n  const fallbackLng = i18n.options ? i18n.options.fallbackLng : false;\n  const lastLng = i18n.languages[i18n.languages.length - 1];\n  if (lng.toLowerCase() === 'cimode') return true;\n  const loadNotPending = (l, n) => {\n    const loadState = i18n.services.backendConnector.state[`${l}|${n}`];\n    return loadState === -1 || loadState === 2;\n  };\n  if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18n.services.backendConnector.backend && i18n.isLanguageChangingTo && !loadNotPending(i18n.isLanguageChangingTo, ns)) return false;\n  if (i18n.hasResourceBundle(lng, ns)) return true;\n  if (!i18n.services.backendConnector.backend || i18n.options.resources && !i18n.options.partialBundledLanguages) return true;\n  if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n  return false;\n}\nfunction hasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce('i18n.languages were undefined or empty', i18n.languages);\n    return true;\n  }\n  const isNewerI18next = i18n.options.ignoreJSONStructure !== undefined;\n  if (!isNewerI18next) {\n    return oldI18nextHasLoadedNamespace(ns, i18n, options);\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n}\nfunction getDisplayName(Component) {\n  return Component.displayName || Component.name || (typeof Component === 'string' && Component.length > 0 ? Component : 'Unknown');\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/withSSR.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withSSR.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withSSR: () => (/* binding */ withSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSSR.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\n\nfunction withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      let {\n        initialI18nStore,\n        initialLanguage,\n        ...rest\n      } = _ref;\n      (0,_useSSR_js__WEBPACK_IMPORTED_MODULE_1__.useSSR)(initialI18nStore, initialLanguage);\n      return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, {\n        ...rest\n      });\n    }\n    I18nextWithSSR.getInitialProps = (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.composeInitialProps)(WrappedComponent);\n    I18nextWithSSR.displayName = `withI18nextSSR(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getDisplayName)(WrappedComponent)})`;\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaTE4bmV4dC9kaXN0L2VzL3dpdGhTU1IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0M7QUFDRDtBQUNjO0FBQ1A7QUFDckM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1IsTUFBTSxrREFBTTtBQUNaLGFBQWEsb0RBQWE7QUFDMUI7QUFDQSxPQUFPO0FBQ1A7QUFDQSxxQ0FBcUMsZ0VBQW1CO0FBQ3hELG1EQUFtRCx5REFBYyxtQkFBbUI7QUFDcEY7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcnlzdGFsYWxpZ25lci8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvd2l0aFNTUi5qcz8xNjc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUVsZW1lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VTU1IgfSBmcm9tICcuL3VzZVNTUi5qcyc7XG5pbXBvcnQgeyBjb21wb3NlSW5pdGlhbFByb3BzIH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmltcG9ydCB7IGdldERpc3BsYXlOYW1lIH0gZnJvbSAnLi91dGlscy5qcyc7XG5leHBvcnQgZnVuY3Rpb24gd2l0aFNTUigpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIEV4dGVuZChXcmFwcGVkQ29tcG9uZW50KSB7XG4gICAgZnVuY3Rpb24gSTE4bmV4dFdpdGhTU1IoX3JlZikge1xuICAgICAgbGV0IHtcbiAgICAgICAgaW5pdGlhbEkxOG5TdG9yZSxcbiAgICAgICAgaW5pdGlhbExhbmd1YWdlLFxuICAgICAgICAuLi5yZXN0XG4gICAgICB9ID0gX3JlZjtcbiAgICAgIHVzZVNTUihpbml0aWFsSTE4blN0b3JlLCBpbml0aWFsTGFuZ3VhZ2UpO1xuICAgICAgcmV0dXJuIGNyZWF0ZUVsZW1lbnQoV3JhcHBlZENvbXBvbmVudCwge1xuICAgICAgICAuLi5yZXN0XG4gICAgICB9KTtcbiAgICB9XG4gICAgSTE4bmV4dFdpdGhTU1IuZ2V0SW5pdGlhbFByb3BzID0gY29tcG9zZUluaXRpYWxQcm9wcyhXcmFwcGVkQ29tcG9uZW50KTtcbiAgICBJMThuZXh0V2l0aFNTUi5kaXNwbGF5TmFtZSA9IGB3aXRoSTE4bmV4dFNTUigke2dldERpc3BsYXlOYW1lKFdyYXBwZWRDb21wb25lbnQpfSlgO1xuICAgIEkxOG5leHRXaXRoU1NSLldyYXBwZWRDb21wb25lbnQgPSBXcmFwcGVkQ29tcG9uZW50O1xuICAgIHJldHVybiBJMThuZXh0V2l0aFNTUjtcbiAgfTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/withSSR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withTranslation.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTranslation: () => (/* binding */ withTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useTranslation.js */ \"(ssr)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nfunction withTranslation(ns) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      let {\n        forwardedRef,\n        ...rest\n      } = _ref;\n      const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(ns, {\n        ...rest,\n        keyPrefix: options.keyPrefix\n      });\n      const passDownProps = {\n        ...rest,\n        t,\n        i18n,\n        tReady: ready\n      };\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n      return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, passDownProps);\n    }\n    I18nextWithTranslation.displayName = `withI18nextTranslation(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getDisplayName)(WrappedComponent)})`;\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n    const forwardRef = (props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(I18nextWithTranslation, Object.assign({}, props, {\n      forwardedRef: ref\n    }));\n    return options.withRef ? (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(forwardRef) : I18nextWithTranslation;\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-i18next/dist/es/withTranslation.js\n");

/***/ })

};
;