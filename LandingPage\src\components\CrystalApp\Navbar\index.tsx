"use client"

import React, { Fragment, useEffect, useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import NavPages from "./NavPages"
import Image from "next/image"

import logo from "/public/images/logo.png"
import logoWhite from "/public/images/logo-white.png"
import { Phone, Telephone, Whatsapp } from "styled-icons/bootstrap"
import { Mail } from "styled-icons/entypo"
import { useTranslation } from "react-i18next"
import LanguageSelector from "@/components/Common/LanguageSelector"
import { Menu, Transition } from "@headlessui/react"
import { getInformation } from "@/services/InformationService"

const Navbar: any = ({ isHomepage = true }: { isHomepage?: boolean }) => {
   const { t } = useTranslation()
   const currentRoute = usePathname()
   const [isSticky, setSticky] = useState<boolean>(false)
   // Sticky Navbar
   useEffect(() => {
      let elementId = document.getElementById("navbar")
      document.addEventListener("scroll", () => {
         if (window.scrollY > 170) {
            elementId?.classList.add("isSticky")
            setSticky(true)
         } else {
            elementId?.classList.remove("isSticky")
            setSticky(false)
         }
      })

      return () => {
         document.removeEventListener("scroll", () => {
            if (window.scrollY > 170) {
               elementId?.classList.add("isSticky")
               setSticky(true)
            } else {
               elementId?.classList.remove("isSticky")
               setSticky(false)
            }
         })
      }
   }, [])

   // Toggle active class
   const [isActive, setActive] = useState<boolean>(false)
   const handleToggleSearchModal = () => {
      setActive(!isActive)
   }

   const [information, setInformation] = useState<any>([])

   const getInformationData = async () => {
      try {
         const response = await getInformation()
         if (response.data) {
            setInformation(response.data.Data)
         }
      } catch (error) {
         console.error("Failed to fetch Information:", error)
      }
   }

   useEffect(() => {
      getInformationData()
   }, [])

   useEffect(() => {
      if (!isHomepage) {
         setSticky(true)
      }
   }, [isHomepage])

   return (
      <>
         <div
            id="header"
            className="header-area bg-gradient-to-l max-md:py-1 from-emerald-600 to-emerald-700"
         >
            <div className="container flex flex-col md:flex-row justify-between md:items-center mx-auto  text-white max-w-[1760px] xl:px-[30px]">
               <Link href={"/orthoclear"} className=" font-bold">
                  {t("navbar.orthoclear")}
               </Link>
               <div className="flex flex-col md:flex-row gap-2  md:gap-4 py-2">
                  <div className="flex flex-row items-center gap-2">
                     <div className="md:self-center  gap-1 flex">
                        <Whatsapp className="w-4 md:w-5" />
                        <a
                           aria-label="Chat on WhatsApp"
                           href="https://wa.me/905389138948"
                           className="text-[12px] md:text-[14px] font-medium transition-all hover:text-stone-200"
                        >
                           {information[0]?.Phone}
                        </a>
                     </div>
                     <div className="md:self-center  gap-1 flex">
                        <Telephone className="w-4 md:w-5" />
                        <a
                           href="tel:+908505321136"
                           className="text-[12px] md:text-[14px] font-medium transition-all hover:text-stone-200"
                        >
                           +(90) ************
                        </a>
                     </div>
                  </div>
                  <div className="md:self-center  gap-2 flex">
                     <Mail className="w-4 md:w-5" />
                     <a
                        href={"mailto:" + information[0]?.Mail}
                        className="text-[12px] md:text-[14px]  font-medium transition-all hover:text-stone-200"
                     >
                        {information[0]?.Mail}
                     </a>
                  </div>
               </div>
            </div>
         </div>
         <div id="navbar" className="navbar-area  relative z-[50] py-[10px] lg:py-0">
            <div
               className={`container mx-auto lg:py-4 xl:py-0 bg-white text-black ${
                  !isSticky && isHomepage && "lg:bg-black/10 lg:text-white"
               }   ${isHomepage && "absolute"} max-w-[1760px] xl:px-[30px] xl:min-w-full`}
            >
               <nav
                  className={`navbar relative flex  max-lg:flex-wrap items-center justify-between ${
                     isActive ? "active" : ""
                  }`}
               >
                  <div className="w-full md:w-fit flex  items-center h-full max-lg:mb-1">
                     <div className="self-center max-w-fit">
                        <Link href="/" className="max-w-fit flex items-center  gap-2  ">
                           <Image
                              src={"/images/logo-light-streamline.png"}
                              alt="logo"
                              width={50}
                              height={50}
                              className=" md:max-w-[60px] lg:max-w-[100px]"
                           />
                           <Image
                              src={!isHomepage ? logo : isSticky ? logo : logoWhite}
                              alt="logo"
                              className="max-w-[120px] max-lg:hidden md:max-w-[120px] xl:max-w-[220px]"
                           />
                           <Image
                              src={logo}
                              alt="logo"
                              className="lg:hidden max-w-[120px] md:max-w-[120px] lg:max-w-[220px]"
                           />
                        </Link>
                     </div>
                     <div className="lg:hidden flex gap-4  ml-auto right-0 top-[2px] items-center">
                        {/* Toggle button */}
                        <LanguageSelector />
                        <button
                           className="navbar-toggler md:hidden "
                           type="button"
                           onClick={handleToggleSearchModal}
                        >
                           <span className="burger-menu text-black cursor-pointer leading-none text-[30px]">
                              <i className="ri-menu-line"></i>
                           </span>
                        </button>
                     </div>
                  </div>
                  <div className="navbar-collapse max-lg:text-center font-bold w-full max-lg:mx-auto  md:ml-8  self-center grow basis-auto md:space-x-[20px] lg:space-x-[50px] xl:space-x-[70px] 2xl:space-x-[100px]">
                     <ul className="navbar-nav self-center flex-row mr-auto md:flex md:space-x-[15px] lg:space-x-[15px]  2xl:space-x-[40px]">
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group">
                           <Link
                              href="/#about"
                              className="text-[20px] md:text-[20px] lg:text-[18px] font-medium transition-all hover:text-gray-400 whitespace-nowrap"
                           >
                              Crystal Aligner
                           </Link>
                        </li>
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group">
                           <Link
                              href="/#orthoclear"
                              className="text-[20px] md:text-[20px] lg:text-[18px] font-medium transition-all hover:text-gray-400 whitespace-nowrap"
                           >
                              OrthoClear
                           </Link>
                        </li>
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group whitespace-nowrap">
                           <Menu as="div" className="relative inline-block text-left">
                              <div className="text-[20px] md:text-[18px]  font-semibold">
                                 <Menu.Button>
                                    {t("navbar.forDoctors")}
                                    <i className="ri-arrow-down-s-line relative top-[2px]"></i>
                                 </Menu.Button>
                              </div>

                              <Transition
                                 as={Fragment}
                                 enter="transition ease-out duration-100"
                                 enterFrom="transform opacity-0 scale-95"
                                 enterTo="transform opacity-100 scale-100"
                                 leave="transition ease-in duration-75"
                                 leaveFrom="transform opacity-100 scale-100"
                                 leaveTo="transform opacity-0 scale-95"
                              >
                                 <Menu.Items className="absolute left-0 z-[999] mt-[10px] w-[250px]  origin-top-right rounded-[10px] bg-white/95 p-[20px]  shadow-lg ring-1 space-y-[12px] ring-black ring-opacity-5 focus:outline-none">
                                    <Menu.Item>
                                       {({ active }) => (
                                          <Link
                                             href="/how-it-works"
                                             className={`text-[15px] font-semibold md:text-[16px]  transition-all hover:text-gray-400 ${
                                                currentRoute === "/#features"
                                                   ? "text-primary-color"
                                                   : "text-black-color"
                                             }`}
                                          >
                                             {t("navbar.features")}
                                          </Link>
                                       )}
                                    </Menu.Item>
                                    <hr />
                                    <div className="w-full h-1" />
                                    <Menu.Item>
                                       {({ active }) => (
                                          <Link
                                             href="/#case"
                                             className={`text-[15px] font-semibold md:text-[16px]  transition-all hover:text-gray-400 ${
                                                currentRoute === "/#case"
                                                   ? "text-primary-color"
                                                   : "text-black-color"
                                             }`}
                                          >
                                             {t("navbar.case")}
                                          </Link>
                                       )}
                                    </Menu.Item>
                                    <hr />
                                    <div className="w-full h-1" />
                                    <Menu.Item>
                                       {({ active }) => (
                                          <Link
                                             href="/#advantages"
                                             className={`text-[15px] font-semibold md:text-[16px] transition-all hover:text-gray-400 ${
                                                currentRoute === "/#advantages"
                                                   ? "text-primary-color"
                                                   : "text-black-color"
                                             }`}
                                          >
                                             {t("advantages.subtitle")}
                                          </Link>
                                       )}
                                    </Menu.Item>
                                    <hr />
                                    <div className="w-full h-1" />
                                    <Menu.Item>
                                       {({ active }) => (
                                          <Link
                                             href="/#curious"
                                             className={`text-[15px] font-semibold md:text-[16px]  transition-all hover:text-gray-400 ${
                                                currentRoute === "/#curious"
                                                   ? "text-primary-color"
                                                   : "text-black-color"
                                             }`}
                                          >
                                             {t("curious-things.title")}
                                          </Link>
                                       )}
                                    </Menu.Item>
                                 </Menu.Items>
                              </Transition>
                           </Menu>
                        </li>
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group whitespace-nowrap">
                           <Menu as="div" className="relative inline-block text-left">
                              <div className="text-[20px] md:text-[18px] font-semibold">
                                 <Menu.Button>
                                    {t("navbar.forPatients")}
                                    <i className="ri-arrow-down-s-line relative top-[2px]"></i>
                                 </Menu.Button>
                              </div>

                              <Transition
                                 as={Fragment}
                                 enter="transition ease-out duration-100"
                                 enterFrom="transform opacity-0 scale-95"
                                 enterTo="transform opacity-100 scale-100"
                                 leave="transition ease-in duration-75"
                                 leaveFrom="transform opacity-100 scale-100"
                                 leaveTo="transform opacity-0 scale-95"
                              >
                                 <Menu.Items className="absolute left-0 z-[999] mt-[10px] w-[250px]  origin-top-right rounded-[10px] bg-white/95 p-[20px]  shadow-lg ring-1 space-y-[12px] ring-black ring-opacity-5 focus:outline-none">
                                    <li>
                                       <Link
                                          href="/#manual"
                                          className={`text-[15px] font-semibold md:text-[16px]  transition-all hover:text-gray-400 ${
                                             currentRoute === "/#manual"
                                                ? "text-primary-color"
                                                : "text-black-color"
                                          }`}
                                       >
                                          {t("navbar.manual")}
                                       </Link>
                                    </li>
                                    <hr />
                                    <li>
                                       <Link
                                          href="/#gallery"
                                          className={`text-[15px] font-semibold md:text-[16px]  transition-all hover:text-gray-400 ${
                                             currentRoute === "/#gallery"
                                                ? "text-primary-color"
                                                : "text-black-color"
                                          }`}
                                       >
                                          {t("navbar.gallery")}
                                       </Link>
                                    </li>
                                 </Menu.Items>
                              </Transition>
                           </Menu>
                        </li>{" "}
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group">
                           <Link
                              href="/#faq"
                              className="text-[20px] md:text-[20px] lg:text-[18px] font-medium transition-all hover:text-gray-400"
                           >
                              {t("navbar.faq")}
                           </Link>
                        </li>
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group">
                           <Link
                              href="/#contact"
                              className={`text-[20px] md:text-[20px] lg:text-[18px] font-medium transition-all hover:text-gray-400`}
                           >
                              {t("navbar.contact")}
                           </Link>
                        </li>
                        <li className="py-[8px] lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group 2xl:w-96 xl:w-40 w-32">
                           <Link
                              href="/find-doctors"
                              className={`text-[20px] md:text-[20px] lg:text-[18px] font-medium transition-all hover:text-gray-400`}
                           >
                              {t("navbar.find-doctor")}
                           </Link>
                        </li>
                        <li className="md:hidden py-[8px] w-full flex justify-center lg:py-[15px] xl:py-[35px] 2xl:py-[38px] relative group">
                           <a
                              href="https://doctor.crystalaligner.com/"
                              rel="noreferrer"
                              target="_blank"
                              className="max-w-fit py-[10px] md:py-[8px] lg:py-[12px] px-[20px] md:px-[15px] lg:px-[20px]  inline-block rounded-[6px] bg-[#6B7280] text-white font-semibold text-[14px] md:text-[13px] lg:text-[20px] xl:text-[18px] transition duration-500 ease-in-out hover:bg-primary-color"
                           >
                              {t("navbar.doctorPanel")}
                           </a>
                        </li>
                     </ul>
                  </div>
                  {/* Other options */}
                  {
                     <div className="flex justify-end w-full max-lg:hidden self-center space-x-[20px] lg:space-x-[25px] ">
                        <div className="mt-[12px] max-lg:hidden xl:visible">
                           <LanguageSelector />
                        </div>
                        <a
                           href="https://doctor.crystalaligner.com/"
                           rel="noreferrer"
                           target="_blank"
                           className="py-[10px] md:py-[8px] lg:py-[12px] px-[20px] md:px-[15px] lg:px-[20px]  inline-block rounded-[6px] bg-[#6B7280] text-white font-semibold text-[14px] md:text-[13px] lg:text-[20px] xl:text-[18px] transition duration-500 ease-in-out hover:bg-primary-color"
                        >
                           {t("navbar.doctorPanel")}
                        </a>
                     </div>
                  }
               </nav>
            </div>
         </div>
      </>
   )
}

export default Navbar
