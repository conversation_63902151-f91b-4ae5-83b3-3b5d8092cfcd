"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_CrystalApp_HeroBanner_tsx";
exports.ids = ["_ssr_src_components_CrystalApp_HeroBanner_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/CrystalApp/HeroBanner.tsx":
/*!**************************************************!*\
  !*** ./src/components/CrystalApp/HeroBanner.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @styled-icons/material-sharp/ContactSupport */ \"(ssr)/./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fslightbox-react */ \"(ssr)/./node_modules/fslightbox-react/index.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fslightbox_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! infinite-zoom-fader */ \"(ssr)/./node_modules/infinite-zoom-fader/dist/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var styled_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! styled-icons/material */ \"(ssr)/./node_modules/@styled-icons/material/index.js\");\n/* harmony import */ var _QuizModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./QuizModal */ \"(ssr)/./src/components/CrystalApp/QuizModal.tsx\");\n/* harmony import */ var styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! styled-icons/heroicons-outline */ \"(ssr)/./node_modules/@styled-icons/heroicons-outline/index.js\");\n/* harmony import */ var styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! styled-icons/ionicons-outline */ \"(ssr)/./node_modules/@styled-icons/ionicons-outline/index.js\");\n/* harmony import */ var _services_SliderService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/SliderService */ \"(ssr)/./src/services/SliderService.ts\");\n/* harmony import */ var _services_HeroService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/HeroService */ \"(ssr)/./src/services/HeroService.ts\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HeroBanner = ()=>{\n    // To open the lightbox change the value of the \"toggler\" prop.\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const [toggler, setToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [brochureToggler, setBrochureToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isQuizModalOpen, setIsQuizModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [heroData, setHeroData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [languageId, setLanguageId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const [sliderText, setSliderText] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [imagesLoading, setImagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [sliderData, setSliderData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            setImagesLoading(true);\n            try {\n                const response = await (0,_services_SliderService__WEBPACK_IMPORTED_MODULE_7__.getSlider)();\n                if (response.data && response.data.Data !== sliderData) {\n                    console.log(\"response.data.Data\", response.data.Data);\n                    setSliderData(response.data.Data); // Only update if data has changed\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch slider data:\", error);\n            } finally{\n                setImagesLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const handleLanguageChange = ()=>{\n            const languageCode = i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].language || localStorage.getItem(\"i18nextLng\");\n            setLanguageId(languageCode === \"tr\" ? 1 : 2);\n        };\n        handleLanguageChange();\n        i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].on(\"languageChanged\", handleLanguageChange);\n        return ()=>{\n            i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].off(\"languageChanged\", handleLanguageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        async function fetchData() {\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroSliderText)(languageId);\n                if (response.data) {\n                    setSliderText(response.data.Data);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch FAQs:\", error);\n            }\n        }\n        fetchData();\n    }, [\n        languageId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const getHeroData = async ()=>{\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroBanner)();\n                setHeroData(response.data.Data);\n            } catch (error) {\n                console.error(\"Failed to fetch hero data:\", error);\n            }\n        };\n        getHeroData();\n    }, []);\n    const responseImages = {\n        desktop: sliderData.map((item)=>{\n            return {\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            };\n        }),\n        mobile: sliderData.map((item)=>({\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            }))\n    };\n    /*   const images = {\r\n    desktop: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n      {\r\n        src: sliderData[4]?.Image ? sliderData[4]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[5]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[6]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[7]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[8]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n    ],\r\n    mobile: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n    ],\r\n  }; */ // change title and subtitle with the images, in every 5 seconds\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.title\"));\n    const [subtitle, setSubtitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.subtitle\"));\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            switch(index){\n                case 0:\n                    setTitle(sliderText[0]?.Title ? sliderText[0]?.Title : t(\"hero.title\"));\n                    setSubtitle(sliderText[0]?.Content ? sliderText[0]?.Content : t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n                case 1:\n                    setTitle(sliderText[1]?.Title ? sliderText[1]?.Title : t(\"hero.title\"));\n                    setSubtitle(sliderText[1]?.Content ? sliderText[1]?.Content : t(\"hero.subtitle2\"));\n                    setIndex(2);\n                    break;\n                case 2:\n                    setTitle(sliderText[2]?.Title ? sliderText[2]?.Title : t(\"hero.title\"));\n                    setSubtitle(sliderText[2]?.Content ? sliderText[2]?.Content : t(\"hero.subtitle3\"));\n                    setIndex(0);\n                    break;\n                case 3:\n                    setTitle(sliderText[3]?.Title ? sliderText[3]?.Title : t(\"hero.title\"));\n                    setSubtitle(sliderText[3]?.Content ? sliderText[3]?.Content : t(\"hero.subtitle4\"));\n                    setIndex(0);\n                    break;\n                default:\n                    setTitle(t(\"hero.title\"));\n                    setSubtitle(t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n            }\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, [\n        index,\n        t,\n        sliderText\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[95vh] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuizModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isQuizModalOpen,\n                setIsOpen: setIsQuizModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: toggler,\n                sources: [\n                    heroData?.VideoLink\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: brochureToggler,\n                sources: [\n                    heroData?.BrochureLink\n                ],\n                type: \"image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            !imagesLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__.InfiniteZoomFader, {\n                images: responseImages,\n                zoom: \"out\",\n                zoomScale: 0.75,\n                zoomTime: 5,\n                zoomMax: 0.25,\n                zoomTimingFunction: \"linear\",\n                transitionTime: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 w-full h-full absolute top-0 left-0 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"home\",\n                        className: \"container max-w-[1760px] xl:px-[30px] h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-[25px] items-center grid-cols-1 md:grid-cols-1 lg:grid-cols-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-[30px] md:space-y-[40px] lg:space-y-[30px] xl:space-y-[40px] lg:max-w-[590px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-h-[200px] md:min-h-[300px] \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: ` text-[35px] md:text-[42px] lg:text-[40px] xl:text-[50px] 2xl:text-[55px] leading-[35px] md:leading-[46px] lg:leading-[55px] xl:leading-[64px] mb-[20px] md:mb-[30px] lg:mb-[20px] xl:mb-[40px] text-white`,\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"100\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-[16px] md:text-[18px] text-stone-300`,\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"200\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:flex items-center space-y-[20px] sm:space-y-[0] sm:space-x-[30px]\",\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"300\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-row  gap-[30px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"#contact\",\n                                                        className: \"inline-block text-white font-semibold text-[16px] md:text-[18px] transition duration-500 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__.ContactSupport, {\n                                                                size: 48,\n                                                                className: \"mr-[3px] mb-[2px]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            t(\"hero.contact\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                        onClick: ()=>setToggler(!toggler),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-[#fff] w-[36px] h-[36px] leading-[36px] pl-[5px] rounded-full text-center text-[22px] transition duration-500 ease-in-out group-hover:bg-primary-color group-hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__.Play, {\n                                                                    size: 28,\n                                                                    className: \"mr-[3px] mb-[2px]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-[16px] md:text-[18px] text-white\",\n                                                                children: t(\"hero.howItWorks\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                onClick: ()=>setBrochureToggler(!brochureToggler),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-[16px] md:text-[18px] text-sky-300 hover:text-sky-400 md:-ml-2 uppercase underline underline-offset-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__.BookOpen, {\n                                                            size: 20,\n                                                            className: \"mr-[6px] mb-[2px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        t(\"hero.brochure\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsQuizModalOpen(true),\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"400\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        className: \" text-stone-200 font-semibold text-[16px] md:text-[18px] text-left italic underline underline-offset-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_material__WEBPACK_IMPORTED_MODULE_13__.Quiz, {\n                                                size: 28,\n                                                className: \"mr-[2px] inline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            t(\"hero.test\"),\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroBanner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CrystalApp/HeroBanner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CrystalApp/QuizModal.tsx":
/*!*************************************************!*\
  !*** ./src/components/CrystalApp/QuizModal.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@headlessui/react */ \"(ssr)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-icons/bootstrap */ \"(ssr)/./node_modules/@styled-icons/bootstrap/index.js\");\n\n\n\n\nconst QuizModal = ({ isOpen, setIsOpen })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onClose: ()=>setIsOpen(false),\n        className: \"relative z-50 \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/30\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex w-screen items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Panel, {\n                    className: \"mx-auto max-w-sm rounded bg-stone-100 py-4 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Title, {\n                            className: `text-xl lg:text-2xl font-bold`,\n                            children: \"Crystal Aligner ile G\\xfcl\\xfcş Testi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Description, {\n                            className: \"my-2 font-semibold\",\n                            children: \"Şeffaf plak tedavisi hakkında daha fazla bilgi almak ister misiniz?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cevabınız evet ise, hemen aşağıdaki butona tıklayarak hekimlerimizle iletişime ge\\xe7in.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://wa.me/905389138948\",\n                            className: \"block mt-6  text-white bg-green-500 py-2 px-4 rounded max-w-fit hover:text-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_3__.Whatsapp, {\n                                    className: \"inline w-5 mr-2 mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Hemen Whatsapp ile iletişime ge\\xe7in\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuizModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CrystalApp/QuizModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/HeroService.ts":
/*!*************************************!*\
  !*** ./src/services/HeroService.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHeroBanner: () => (/* binding */ getHeroBanner),\n/* harmony export */   getHeroSliderText: () => (/* binding */ getHeroSliderText)\n/* harmony export */ });\n/* harmony import */ var _ApiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ApiService */ \"(ssr)/./src/services/ApiService.ts\");\n\nconst getHeroBanner = async ()=>{\n    return await (0,_ApiService__WEBPACK_IMPORTED_MODULE_0__.GetWithBasic)(`Hero`);\n};\nconst getHeroSliderText = async (languageId)=>{\n    return await (0,_ApiService__WEBPACK_IMPORTED_MODULE_0__.GetWithBasic)(`SliderText/GetByLangugageId/${languageId}`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc2VydmljZXMvSGVyb1NlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBRXJDLE1BQU1DLGdCQUFnQjtJQUMzQixPQUFPLE1BQU1ELHlEQUFZQSxDQUFDLENBQUMsSUFBSSxDQUFDO0FBQ2xDLEVBQUU7QUFFSyxNQUFNRSxvQkFBb0IsT0FBT0M7SUFDdEMsT0FBTyxNQUFNSCx5REFBWUEsQ0FBQyxDQUFDLDRCQUE0QixFQUFFRyxXQUFXLENBQUM7QUFDdkUsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vc3JjL3NlcnZpY2VzL0hlcm9TZXJ2aWNlLnRzPzQyZTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR2V0V2l0aEJhc2ljIH0gZnJvbSBcIi4vQXBpU2VydmljZVwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldEhlcm9CYW5uZXIgPSBhc3luYyAoKSA9PiB7XHJcbiAgcmV0dXJuIGF3YWl0IEdldFdpdGhCYXNpYyhgSGVyb2ApO1xyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldEhlcm9TbGlkZXJUZXh0ID0gYXN5bmMgKGxhbmd1YWdlSWQ6IG51bWJlcikgPT4ge1xyXG4gIHJldHVybiBhd2FpdCBHZXRXaXRoQmFzaWMoYFNsaWRlclRleHQvR2V0QnlMYW5ndWdhZ2VJZC8ke2xhbmd1YWdlSWR9YCk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJHZXRXaXRoQmFzaWMiLCJnZXRIZXJvQmFubmVyIiwiZ2V0SGVyb1NsaWRlclRleHQiLCJsYW5ndWFnZUlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/services/HeroService.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/SliderService.ts":
/*!***************************************!*\
  !*** ./src/services/SliderService.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSlider: () => (/* binding */ getSlider)\n/* harmony export */ });\n/* harmony import */ var _ApiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ApiService */ \"(ssr)/./src/services/ApiService.ts\");\n\nconst getSlider = async ()=>{\n    return await (0,_ApiService__WEBPACK_IMPORTED_MODULE_0__.GetWithBasic)(`Slider`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc2VydmljZXMvU2xpZGVyU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQztBQUVwQyxNQUFNQyxZQUFZO0lBQ3RCLE9BQU8sTUFBTUQseURBQVlBLENBQUMsQ0FBQyxNQUFNLENBQUM7QUFDckMsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vc3JjL3NlcnZpY2VzL1NsaWRlclNlcnZpY2UudHM/ZGE1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHZXRXaXRoQmFzaWMgfSBmcm9tIFwiLi9BcGlTZXJ2aWNlXCJcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRTbGlkZXIgPSBhc3luYyAoKSA9PiB7XHJcbiAgIHJldHVybiBhd2FpdCBHZXRXaXRoQmFzaWMoYFNsaWRlcmApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkdldFdpdGhCYXNpYyIsImdldFNsaWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/services/SliderService.ts\n");

/***/ })

};
;