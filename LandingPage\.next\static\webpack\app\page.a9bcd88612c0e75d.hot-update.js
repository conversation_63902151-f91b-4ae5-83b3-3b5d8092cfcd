"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/services/ApiService.ts":
/*!************************************!*\
  !*** ./src/services/ApiService.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetWithBasic: function() { return /* binding */ GetWithBasic; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n// Base Service that will be used to make API calls, it will be extended by other services. Takes base URL and headers as input and returns a promise. It also has a method to handle errors and a method to handle the response. Uses Axios to make API calls. Gets Basic Auth credentials from the environment variables.\n// Path: src/services/ApiService.ts\n\nconst handleError = (error)=>{\n    return Promise.reject(error);\n};\n// const NEXT_PUBLIC_APU_URL = \"https://api.crystalaligner.com/api/v2/\";\nconst NEXT_PUBLIC_APU_URL = \"http://localhost:5000/api/v2/\";\nconst NEXT_PUBLIC_API_USERNAME = \"CrystalAligner2023\";\nconst NEXT_PUBLIC_API_PASSWORD = \"f98s8Y44sQ7R\";\nconst GetWithBasic = async (url)=>{\n    try {\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(NEXT_PUBLIC_APU_URL + url, // basic auth\n        {\n            auth: {\n                username: NEXT_PUBLIC_API_USERNAME || \"\",\n                password: NEXT_PUBLIC_API_PASSWORD || \"\"\n            },\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Accept: \"application/json\",\n                \"Access-Control-Allow-Origin\": \"*\"\n            },\n            withCredentials: true\n        });\n        console.log(\"response\", response);\n        return response;\n    } catch (error) {\n        return handleError(error);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/ApiService.ts\n"));

/***/ })

});