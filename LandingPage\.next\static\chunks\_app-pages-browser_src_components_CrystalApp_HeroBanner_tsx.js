"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_CrystalApp_HeroBanner_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@styled-icons/heroicons-outline/BookOpen/BookOpen.esm.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@styled-icons/heroicons-outline/BookOpen/BookOpen.esm.js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookOpen: function() { return /* binding */ BookOpen; },\n/* harmony export */   BookOpenDimensions: function() { return /* binding */ BookOpenDimensions; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @styled-icons/styled-icon */ \"(app-pages-browser)/./node_modules/@styled-icons/styled-icon/index.esm.js\");\n\n\n\nvar BookOpen = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var attrs = {\n    \"fill\": \"none\",\n    \"xmlns\": \"http://www.w3.org/2000/svg\",\n    \"stroke\": \"currentColor\"\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__.StyledIconBase, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    iconAttrs: attrs,\n    iconVerticalAlign: \"middle\",\n    iconViewBox: \"0 0 24 24\"\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25\"\n  }));\n});\nBookOpen.displayName = 'BookOpen';\nvar BookOpenDimensions = {\n  height: 24,\n  width: 24\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@styled-icons/heroicons-outline/BookOpen/BookOpen.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@styled-icons/ionicons-outline/Play/Play.esm.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@styled-icons/ionicons-outline/Play/Play.esm.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Play: function() { return /* binding */ Play; },\n/* harmony export */   PlayDimensions: function() { return /* binding */ PlayDimensions; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @styled-icons/styled-icon */ \"(app-pages-browser)/./node_modules/@styled-icons/styled-icon/index.esm.js\");\n\n\n\nvar Play = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var attrs = {\n    \"fill\": \"currentColor\",\n    \"xmlns\": \"http://www.w3.org/2000/svg\"\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__.StyledIconBase, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    iconAttrs: attrs,\n    iconVerticalAlign: \"middle\",\n    iconViewBox: \"0 0 512 512\"\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeMiterlimit: 10,\n    strokeWidth: 32,\n    d: \"M112 111v290c0 17.44 17 28.52 31 20.16l247.9-148.37c12.12-7.25 12.12-26.33 0-33.58L143 90.84c-14-8.36-31 2.72-31 20.16z\"\n  }));\n});\nPlay.displayName = 'Play';\nvar PlayDimensions = {\n  height: 512,\n  width: 512\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac3R5bGVkLWljb25zL2lvbmljb25zLW91dGxpbmUvUGxheS9QbGF5LmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0Q7QUFDdkI7QUFDNEI7QUFDcEQsd0JBQXdCLDZDQUFnQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixnREFBbUIsQ0FBQyxxRUFBYyxFQUFFLDBFQUFRO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUcsZ0JBQWdCLGdEQUFtQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILENBQUM7QUFDRDtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ac3R5bGVkLWljb25zL2lvbmljb25zLW91dGxpbmUvUGxheS9QbGF5LmVzbS5qcz9iMmQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9leHRlbmRzXCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTdHlsZWRJY29uQmFzZSB9IGZyb20gJ0BzdHlsZWQtaWNvbnMvc3R5bGVkLWljb24nO1xuZXhwb3J0IHZhciBQbGF5ID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoZnVuY3Rpb24gKHByb3BzLCByZWYpIHtcbiAgdmFyIGF0dHJzID0ge1xuICAgIFwiZmlsbFwiOiBcImN1cnJlbnRDb2xvclwiLFxuICAgIFwieG1sbnNcIjogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gIH07XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChTdHlsZWRJY29uQmFzZSwgX2V4dGVuZHMoe1xuICAgIGljb25BdHRyczogYXR0cnMsXG4gICAgaWNvblZlcnRpY2FsQWxpZ246IFwibWlkZGxlXCIsXG4gICAgaWNvblZpZXdCb3g6IFwiMCAwIDUxMiA1MTJcIlxuICB9LCBwcm9wcywge1xuICAgIHJlZjogcmVmXG4gIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHN0cm9rZTogXCJjdXJyZW50Q29sb3JcIixcbiAgICBzdHJva2VNaXRlcmxpbWl0OiAxMCxcbiAgICBzdHJva2VXaWR0aDogMzIsXG4gICAgZDogXCJNMTEyIDExMXYyOTBjMCAxNy40NCAxNyAyOC41MiAzMSAyMC4xNmwyNDcuOS0xNDguMzdjMTIuMTItNy4yNSAxMi4xMi0yNi4zMyAwLTMzLjU4TDE0MyA5MC44NGMtMTQtOC4zNi0zMSAyLjcyLTMxIDIwLjE2elwiXG4gIH0pKTtcbn0pO1xuUGxheS5kaXNwbGF5TmFtZSA9ICdQbGF5JztcbmV4cG9ydCB2YXIgUGxheURpbWVuc2lvbnMgPSB7XG4gIGhlaWdodDogNTEyLFxuICB3aWR0aDogNTEyXG59OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@styled-icons/ionicons-outline/Play/Play.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContactSupport: function() { return /* binding */ ContactSupport; },\n/* harmony export */   ContactSupportDimensions: function() { return /* binding */ ContactSupportDimensions; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @styled-icons/styled-icon */ \"(app-pages-browser)/./node_modules/@styled-icons/styled-icon/index.esm.js\");\n\n\n\nvar ContactSupport = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var attrs = {\n    \"fill\": \"currentColor\",\n    \"xmlns\": \"http://www.w3.org/2000/svg\"\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__.StyledIconBase, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    iconAttrs: attrs,\n    iconVerticalAlign: \"middle\",\n    iconViewBox: \"0 0 24 24\"\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n    d: \"M11.5 2C6.81 2 3 5.81 3 10.5S6.81 19 11.5 19h.5v3c4.86-2.34 8-7 8-11.5C20 5.81 16.19 2 11.5 2zm1 14.5h-2v-2h2v2zm0-3.5h-2c0-3.25 3-3 3-5 0-1.1-.9-2-2-2s-2 .9-2 2h-2c0-2.21 1.79-4 4-4s4 1.79 4 4c0 2.5-3 2.75-3 5z\"\n  }));\n});\nContactSupport.displayName = 'ContactSupport';\nvar ContactSupportDimensions = {\n  height: 24,\n  width: 24\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac3R5bGVkLWljb25zL21hdGVyaWFsLXNoYXJwL0NvbnRhY3RTdXBwb3J0L0NvbnRhY3RTdXBwb3J0LmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0Q7QUFDdkI7QUFDNEI7QUFDcEQsa0NBQWtDLDZDQUFnQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixnREFBbUIsQ0FBQyxxRUFBYyxFQUFFLDBFQUFRO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLEdBQUcsZ0JBQWdCLGdEQUFtQjtBQUN0QztBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0Q7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHN0eWxlZC1pY29ucy9tYXRlcmlhbC1zaGFycC9Db250YWN0U3VwcG9ydC9Db250YWN0U3VwcG9ydC5lc20uanM/MGM2OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXh0ZW5kc1wiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU3R5bGVkSWNvbkJhc2UgfSBmcm9tICdAc3R5bGVkLWljb25zL3N0eWxlZC1pY29uJztcbmV4cG9ydCB2YXIgQ29udGFjdFN1cHBvcnQgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikge1xuICB2YXIgYXR0cnMgPSB7XG4gICAgXCJmaWxsXCI6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJ4bWxuc1wiOiBcImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgfTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFN0eWxlZEljb25CYXNlLCBfZXh0ZW5kcyh7XG4gICAgaWNvbkF0dHJzOiBhdHRycyxcbiAgICBpY29uVmVydGljYWxBbGlnbjogXCJtaWRkbGVcIixcbiAgICBpY29uVmlld0JveDogXCIwIDAgMjQgMjRcIlxuICB9LCBwcm9wcywge1xuICAgIHJlZjogcmVmXG4gIH0pLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIGQ6IFwiTTExLjUgMkM2LjgxIDIgMyA1LjgxIDMgMTAuNVM2LjgxIDE5IDExLjUgMTloLjV2M2M0Ljg2LTIuMzQgOC03IDgtMTEuNUMyMCA1LjgxIDE2LjE5IDIgMTEuNSAyem0xIDE0LjVoLTJ2LTJoMnYyem0wLTMuNWgtMmMwLTMuMjUgMy0zIDMtNSAwLTEuMS0uOS0yLTItMnMtMiAuOS0yIDJoLTJjMC0yLjIxIDEuNzktNCA0LTRzNCAxLjc5IDQgNGMwIDIuNS0zIDIuNzUtMyA1elwiXG4gIH0pKTtcbn0pO1xuQ29udGFjdFN1cHBvcnQuZGlzcGxheU5hbWUgPSAnQ29udGFjdFN1cHBvcnQnO1xuZXhwb3J0IHZhciBDb250YWN0U3VwcG9ydERpbWVuc2lvbnMgPSB7XG4gIGhlaWdodDogMjQsXG4gIHdpZHRoOiAyNFxufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@styled-icons/material/Quiz/Quiz.esm.js":
/*!**************************************************************!*\
  !*** ./node_modules/@styled-icons/material/Quiz/Quiz.esm.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Quiz: function() { return /* binding */ Quiz; },\n/* harmony export */   QuizDimensions: function() { return /* binding */ QuizDimensions; }\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @styled-icons/styled-icon */ \"(app-pages-browser)/./node_modules/@styled-icons/styled-icon/index.esm.js\");\n\n\n\nvar Quiz = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function (props, ref) {\n  var attrs = {\n    \"fill\": \"currentColor\",\n    \"xmlns\": \"http://www.w3.org/2000/svg\"\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(_styled_icons_styled_icon__WEBPACK_IMPORTED_MODULE_2__.StyledIconBase, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    iconAttrs: attrs,\n    iconVerticalAlign: \"middle\",\n    iconViewBox: \"0 0 24 24\"\n  }, props, {\n    ref: ref\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n    d: \"M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6z\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"path\", {\n    d: \"M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-5.99 13c-.59 0-1.05-.47-1.05-1.05 0-.59.47-1.04 1.05-1.04.59 0 1.04.45 1.04 1.04-.01.58-.45 1.05-1.04 1.05zm2.5-6.17c-.63.93-1.23 1.21-1.56 1.81-.13.24-.18.4-.18 1.18h-1.52c0-.41-.06-1.08.26-1.65.41-.73 1.18-1.16 1.63-1.8.48-.68.21-1.94-1.14-1.94-.88 0-1.32.67-1.5 1.23l-1.37-.57C11.51 5.96 12.52 5 13.99 5c1.23 0 2.08.56 2.51 1.26.37.61.58 1.73.01 2.57z\"\n  }));\n});\nQuiz.displayName = 'Quiz';\nvar QuizDimensions = {\n  height: 24,\n  width: 24\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@styled-icons/material/Quiz/Quiz.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/infinite-zoom-fader/dist/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/infinite-zoom-fader/dist/esm/index.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteZoomFader: function() { return /* binding */ InfiniteZoomFader; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar propTypes = {exports: {}};\n\nvar reactIs = {exports: {}};\n\nvar reactIs_production_min = {};\n\n/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactIs_production_min;\n\nfunction requireReactIs_production_min () {\n\tif (hasRequiredReactIs_production_min) return reactIs_production_min;\n\thasRequiredReactIs_production_min = 1;\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\n\tSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\n\tfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}reactIs_production_min.AsyncMode=l;reactIs_production_min.ConcurrentMode=m;reactIs_production_min.ContextConsumer=k;reactIs_production_min.ContextProvider=h;reactIs_production_min.Element=c;reactIs_production_min.ForwardRef=n;reactIs_production_min.Fragment=e;reactIs_production_min.Lazy=t;reactIs_production_min.Memo=r;reactIs_production_min.Portal=d;\n\treactIs_production_min.Profiler=g;reactIs_production_min.StrictMode=f;reactIs_production_min.Suspense=p;reactIs_production_min.isAsyncMode=function(a){return A(a)||z(a)===l};reactIs_production_min.isConcurrentMode=A;reactIs_production_min.isContextConsumer=function(a){return z(a)===k};reactIs_production_min.isContextProvider=function(a){return z(a)===h};reactIs_production_min.isElement=function(a){return \"object\"===typeof a&&null!==a&&a.$$typeof===c};reactIs_production_min.isForwardRef=function(a){return z(a)===n};reactIs_production_min.isFragment=function(a){return z(a)===e};reactIs_production_min.isLazy=function(a){return z(a)===t};\n\treactIs_production_min.isMemo=function(a){return z(a)===r};reactIs_production_min.isPortal=function(a){return z(a)===d};reactIs_production_min.isProfiler=function(a){return z(a)===g};reactIs_production_min.isStrictMode=function(a){return z(a)===f};reactIs_production_min.isSuspense=function(a){return z(a)===p};\n\treactIs_production_min.isValidElementType=function(a){return \"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};reactIs_production_min.typeOf=z;\n\treturn reactIs_production_min;\n}\n\nvar reactIs_development = {};\n\n/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactIs_development;\n\nfunction requireReactIs_development () {\n\tif (hasRequiredReactIs_development) return reactIs_development;\n\thasRequiredReactIs_development = 1;\n\n\n\n\tif (true) {\n\t  (function() {\n\n\t// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n\t// nor polyfill, then a plain number is used for performance.\n\tvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\tvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\n\tvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\n\tvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\n\tvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\n\tvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\n\tvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\n\tvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n\t// (unstable) APIs that have been removed. Can we remove the symbols?\n\n\tvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\n\tvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\n\tvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\tvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\n\tvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\n\tvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\n\tvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\n\tvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\n\tvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\n\tvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\n\tvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\n\tfunction isValidElementType(type) {\n\t  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n\t  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n\t}\n\n\tfunction typeOf(object) {\n\t  if (typeof object === 'object' && object !== null) {\n\t    var $$typeof = object.$$typeof;\n\n\t    switch ($$typeof) {\n\t      case REACT_ELEMENT_TYPE:\n\t        var type = object.type;\n\n\t        switch (type) {\n\t          case REACT_ASYNC_MODE_TYPE:\n\t          case REACT_CONCURRENT_MODE_TYPE:\n\t          case REACT_FRAGMENT_TYPE:\n\t          case REACT_PROFILER_TYPE:\n\t          case REACT_STRICT_MODE_TYPE:\n\t          case REACT_SUSPENSE_TYPE:\n\t            return type;\n\n\t          default:\n\t            var $$typeofType = type && type.$$typeof;\n\n\t            switch ($$typeofType) {\n\t              case REACT_CONTEXT_TYPE:\n\t              case REACT_FORWARD_REF_TYPE:\n\t              case REACT_LAZY_TYPE:\n\t              case REACT_MEMO_TYPE:\n\t              case REACT_PROVIDER_TYPE:\n\t                return $$typeofType;\n\n\t              default:\n\t                return $$typeof;\n\t            }\n\n\t        }\n\n\t      case REACT_PORTAL_TYPE:\n\t        return $$typeof;\n\t    }\n\t  }\n\n\t  return undefined;\n\t} // AsyncMode is deprecated along with isAsyncMode\n\n\tvar AsyncMode = REACT_ASYNC_MODE_TYPE;\n\tvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\n\tvar ContextConsumer = REACT_CONTEXT_TYPE;\n\tvar ContextProvider = REACT_PROVIDER_TYPE;\n\tvar Element = REACT_ELEMENT_TYPE;\n\tvar ForwardRef = REACT_FORWARD_REF_TYPE;\n\tvar Fragment = REACT_FRAGMENT_TYPE;\n\tvar Lazy = REACT_LAZY_TYPE;\n\tvar Memo = REACT_MEMO_TYPE;\n\tvar Portal = REACT_PORTAL_TYPE;\n\tvar Profiler = REACT_PROFILER_TYPE;\n\tvar StrictMode = REACT_STRICT_MODE_TYPE;\n\tvar Suspense = REACT_SUSPENSE_TYPE;\n\tvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\n\tfunction isAsyncMode(object) {\n\t  {\n\t    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n\t      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n\t      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n\t    }\n\t  }\n\n\t  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n\t}\n\tfunction isConcurrentMode(object) {\n\t  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n\t}\n\tfunction isContextConsumer(object) {\n\t  return typeOf(object) === REACT_CONTEXT_TYPE;\n\t}\n\tfunction isContextProvider(object) {\n\t  return typeOf(object) === REACT_PROVIDER_TYPE;\n\t}\n\tfunction isElement(object) {\n\t  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n\t}\n\tfunction isForwardRef(object) {\n\t  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n\t}\n\tfunction isFragment(object) {\n\t  return typeOf(object) === REACT_FRAGMENT_TYPE;\n\t}\n\tfunction isLazy(object) {\n\t  return typeOf(object) === REACT_LAZY_TYPE;\n\t}\n\tfunction isMemo(object) {\n\t  return typeOf(object) === REACT_MEMO_TYPE;\n\t}\n\tfunction isPortal(object) {\n\t  return typeOf(object) === REACT_PORTAL_TYPE;\n\t}\n\tfunction isProfiler(object) {\n\t  return typeOf(object) === REACT_PROFILER_TYPE;\n\t}\n\tfunction isStrictMode(object) {\n\t  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n\t}\n\tfunction isSuspense(object) {\n\t  return typeOf(object) === REACT_SUSPENSE_TYPE;\n\t}\n\n\treactIs_development.AsyncMode = AsyncMode;\n\treactIs_development.ConcurrentMode = ConcurrentMode;\n\treactIs_development.ContextConsumer = ContextConsumer;\n\treactIs_development.ContextProvider = ContextProvider;\n\treactIs_development.Element = Element;\n\treactIs_development.ForwardRef = ForwardRef;\n\treactIs_development.Fragment = Fragment;\n\treactIs_development.Lazy = Lazy;\n\treactIs_development.Memo = Memo;\n\treactIs_development.Portal = Portal;\n\treactIs_development.Profiler = Profiler;\n\treactIs_development.StrictMode = StrictMode;\n\treactIs_development.Suspense = Suspense;\n\treactIs_development.isAsyncMode = isAsyncMode;\n\treactIs_development.isConcurrentMode = isConcurrentMode;\n\treactIs_development.isContextConsumer = isContextConsumer;\n\treactIs_development.isContextProvider = isContextProvider;\n\treactIs_development.isElement = isElement;\n\treactIs_development.isForwardRef = isForwardRef;\n\treactIs_development.isFragment = isFragment;\n\treactIs_development.isLazy = isLazy;\n\treactIs_development.isMemo = isMemo;\n\treactIs_development.isPortal = isPortal;\n\treactIs_development.isProfiler = isProfiler;\n\treactIs_development.isStrictMode = isStrictMode;\n\treactIs_development.isSuspense = isSuspense;\n\treactIs_development.isValidElementType = isValidElementType;\n\treactIs_development.typeOf = typeOf;\n\t  })();\n\t}\n\treturn reactIs_development;\n}\n\nvar hasRequiredReactIs;\n\nfunction requireReactIs () {\n\tif (hasRequiredReactIs) return reactIs.exports;\n\thasRequiredReactIs = 1;\n\n\tif (false) {} else {\n\t  reactIs.exports = requireReactIs_development();\n\t}\n\treturn reactIs.exports;\n}\n\n/*\nobject-assign\n(c) Sindre Sorhus\n@license MIT\n*/\n\nvar objectAssign;\nvar hasRequiredObjectAssign;\n\nfunction requireObjectAssign () {\n\tif (hasRequiredObjectAssign) return objectAssign;\n\thasRequiredObjectAssign = 1;\n\t/* eslint-disable no-unused-vars */\n\tvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\n\tvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\tvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\n\tfunction toObject(val) {\n\t\tif (val === null || val === undefined) {\n\t\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t\t}\n\n\t\treturn Object(val);\n\t}\n\n\tfunction shouldUseNative() {\n\t\ttry {\n\t\t\tif (!Object.assign) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\t\ttest1[5] = 'de';\n\t\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\t\tvar test2 = {};\n\t\t\tfor (var i = 0; i < 10; i++) {\n\t\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t\t}\n\t\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\t\treturn test2[n];\n\t\t\t});\n\t\t\tif (order2.join('') !== '0123456789') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\t\tvar test3 = {};\n\t\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\t\ttest3[letter] = letter;\n\t\t\t});\n\t\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn true;\n\t\t} catch (err) {\n\t\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tobjectAssign = shouldUseNative() ? Object.assign : function (target, source) {\n\t\tvar from;\n\t\tvar to = toObject(target);\n\t\tvar symbols;\n\n\t\tfor (var s = 1; s < arguments.length; s++) {\n\t\t\tfrom = Object(arguments[s]);\n\n\t\t\tfor (var key in from) {\n\t\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\t\tto[key] = from[key];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (getOwnPropertySymbols) {\n\t\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn to;\n\t};\n\treturn objectAssign;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar ReactPropTypesSecret_1;\nvar hasRequiredReactPropTypesSecret;\n\nfunction requireReactPropTypesSecret () {\n\tif (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;\n\thasRequiredReactPropTypesSecret = 1;\n\n\tvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\n\tReactPropTypesSecret_1 = ReactPropTypesSecret;\n\treturn ReactPropTypesSecret_1;\n}\n\nvar has;\nvar hasRequiredHas;\n\nfunction requireHas () {\n\tif (hasRequiredHas) return has;\n\thasRequiredHas = 1;\n\thas = Function.call.bind(Object.prototype.hasOwnProperty);\n\treturn has;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar checkPropTypes_1;\nvar hasRequiredCheckPropTypes;\n\nfunction requireCheckPropTypes () {\n\tif (hasRequiredCheckPropTypes) return checkPropTypes_1;\n\thasRequiredCheckPropTypes = 1;\n\n\tvar printWarning = function() {};\n\n\tif (true) {\n\t  var ReactPropTypesSecret = requireReactPropTypesSecret();\n\t  var loggedTypeFailures = {};\n\t  var has = requireHas();\n\n\t  printWarning = function(text) {\n\t    var message = 'Warning: ' + text;\n\t    if (typeof console !== 'undefined') {\n\t      console.error(message);\n\t    }\n\t    try {\n\t      // --- Welcome to debugging React ---\n\t      // This error was thrown as a convenience so that you can use this stack\n\t      // to find the callsite that caused this warning to fire.\n\t      throw new Error(message);\n\t    } catch (x) { /**/ }\n\t  };\n\t}\n\n\t/**\n\t * Assert that the values match with the type specs.\n\t * Error messages are memorized and will only be shown once.\n\t *\n\t * @param {object} typeSpecs Map of name to a ReactPropType\n\t * @param {object} values Runtime values that need to be type-checked\n\t * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n\t * @param {string} componentName Name of the component for error messages.\n\t * @param {?Function} getStack Returns the component stack.\n\t * @private\n\t */\n\tfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n\t  if (true) {\n\t    for (var typeSpecName in typeSpecs) {\n\t      if (has(typeSpecs, typeSpecName)) {\n\t        var error;\n\t        // Prop type validation may throw. In case they do, we don't want to\n\t        // fail the render phase where it didn't fail before. So we log it.\n\t        // After these have been cleaned up, we'll let them throw.\n\t        try {\n\t          // This is intentionally an invariant that gets caught. It's the same\n\t          // behavior as without this statement except with a better message.\n\t          if (typeof typeSpecs[typeSpecName] !== 'function') {\n\t            var err = Error(\n\t              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n\t              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n\t              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n\t            );\n\t            err.name = 'Invariant Violation';\n\t            throw err;\n\t          }\n\t          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n\t        } catch (ex) {\n\t          error = ex;\n\t        }\n\t        if (error && !(error instanceof Error)) {\n\t          printWarning(\n\t            (componentName || 'React class') + ': type specification of ' +\n\t            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n\t            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n\t            'You may have forgotten to pass an argument to the type checker ' +\n\t            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n\t            'shape all require an argument).'\n\t          );\n\t        }\n\t        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n\t          // Only monitor this failure once because there tends to be a lot of the\n\t          // same error.\n\t          loggedTypeFailures[error.message] = true;\n\n\t          var stack = getStack ? getStack() : '';\n\n\t          printWarning(\n\t            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n\t          );\n\t        }\n\t      }\n\t    }\n\t  }\n\t}\n\n\t/**\n\t * Resets warning cache when testing.\n\t *\n\t * @private\n\t */\n\tcheckPropTypes.resetWarningCache = function() {\n\t  if (true) {\n\t    loggedTypeFailures = {};\n\t  }\n\t};\n\n\tcheckPropTypes_1 = checkPropTypes;\n\treturn checkPropTypes_1;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar factoryWithTypeCheckers;\nvar hasRequiredFactoryWithTypeCheckers;\n\nfunction requireFactoryWithTypeCheckers () {\n\tif (hasRequiredFactoryWithTypeCheckers) return factoryWithTypeCheckers;\n\thasRequiredFactoryWithTypeCheckers = 1;\n\n\tvar ReactIs = requireReactIs();\n\tvar assign = requireObjectAssign();\n\n\tvar ReactPropTypesSecret = requireReactPropTypesSecret();\n\tvar has = requireHas();\n\tvar checkPropTypes = requireCheckPropTypes();\n\n\tvar printWarning = function() {};\n\n\tif (true) {\n\t  printWarning = function(text) {\n\t    var message = 'Warning: ' + text;\n\t    if (typeof console !== 'undefined') {\n\t      console.error(message);\n\t    }\n\t    try {\n\t      // --- Welcome to debugging React ---\n\t      // This error was thrown as a convenience so that you can use this stack\n\t      // to find the callsite that caused this warning to fire.\n\t      throw new Error(message);\n\t    } catch (x) {}\n\t  };\n\t}\n\n\tfunction emptyFunctionThatReturnsNull() {\n\t  return null;\n\t}\n\n\tfactoryWithTypeCheckers = function(isValidElement, throwOnDirectAccess) {\n\t  /* global Symbol */\n\t  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n\t  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n\t  /**\n\t   * Returns the iterator method function contained on the iterable object.\n\t   *\n\t   * Be sure to invoke the function with the iterable as context:\n\t   *\n\t   *     var iteratorFn = getIteratorFn(myIterable);\n\t   *     if (iteratorFn) {\n\t   *       var iterator = iteratorFn.call(myIterable);\n\t   *       ...\n\t   *     }\n\t   *\n\t   * @param {?object} maybeIterable\n\t   * @return {?function}\n\t   */\n\t  function getIteratorFn(maybeIterable) {\n\t    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n\t    if (typeof iteratorFn === 'function') {\n\t      return iteratorFn;\n\t    }\n\t  }\n\n\t  /**\n\t   * Collection of methods that allow declaration and validation of props that are\n\t   * supplied to React components. Example usage:\n\t   *\n\t   *   var Props = require('ReactPropTypes');\n\t   *   var MyArticle = React.createClass({\n\t   *     propTypes: {\n\t   *       // An optional string prop named \"description\".\n\t   *       description: Props.string,\n\t   *\n\t   *       // A required enum prop named \"category\".\n\t   *       category: Props.oneOf(['News','Photos']).isRequired,\n\t   *\n\t   *       // A prop named \"dialog\" that requires an instance of Dialog.\n\t   *       dialog: Props.instanceOf(Dialog).isRequired\n\t   *     },\n\t   *     render: function() { ... }\n\t   *   });\n\t   *\n\t   * A more formal specification of how these methods are used:\n\t   *\n\t   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n\t   *   decl := ReactPropTypes.{type}(.isRequired)?\n\t   *\n\t   * Each and every declaration produces a function with the same signature. This\n\t   * allows the creation of custom validation functions. For example:\n\t   *\n\t   *  var MyLink = React.createClass({\n\t   *    propTypes: {\n\t   *      // An optional string or URI prop named \"href\".\n\t   *      href: function(props, propName, componentName) {\n\t   *        var propValue = props[propName];\n\t   *        if (propValue != null && typeof propValue !== 'string' &&\n\t   *            !(propValue instanceof URI)) {\n\t   *          return new Error(\n\t   *            'Expected a string or an URI for ' + propName + ' in ' +\n\t   *            componentName\n\t   *          );\n\t   *        }\n\t   *      }\n\t   *    },\n\t   *    render: function() {...}\n\t   *  });\n\t   *\n\t   * @internal\n\t   */\n\n\t  var ANONYMOUS = '<<anonymous>>';\n\n\t  // Important!\n\t  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n\t  var ReactPropTypes = {\n\t    array: createPrimitiveTypeChecker('array'),\n\t    bigint: createPrimitiveTypeChecker('bigint'),\n\t    bool: createPrimitiveTypeChecker('boolean'),\n\t    func: createPrimitiveTypeChecker('function'),\n\t    number: createPrimitiveTypeChecker('number'),\n\t    object: createPrimitiveTypeChecker('object'),\n\t    string: createPrimitiveTypeChecker('string'),\n\t    symbol: createPrimitiveTypeChecker('symbol'),\n\n\t    any: createAnyTypeChecker(),\n\t    arrayOf: createArrayOfTypeChecker,\n\t    element: createElementTypeChecker(),\n\t    elementType: createElementTypeTypeChecker(),\n\t    instanceOf: createInstanceTypeChecker,\n\t    node: createNodeChecker(),\n\t    objectOf: createObjectOfTypeChecker,\n\t    oneOf: createEnumTypeChecker,\n\t    oneOfType: createUnionTypeChecker,\n\t    shape: createShapeTypeChecker,\n\t    exact: createStrictShapeTypeChecker,\n\t  };\n\n\t  /**\n\t   * inlined Object.is polyfill to avoid requiring consumers ship their own\n\t   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\t   */\n\t  /*eslint-disable no-self-compare*/\n\t  function is(x, y) {\n\t    // SameValue algorithm\n\t    if (x === y) {\n\t      // Steps 1-5, 7-10\n\t      // Steps 6.b-6.e: +0 != -0\n\t      return x !== 0 || 1 / x === 1 / y;\n\t    } else {\n\t      // Step 6.a: NaN == NaN\n\t      return x !== x && y !== y;\n\t    }\n\t  }\n\t  /*eslint-enable no-self-compare*/\n\n\t  /**\n\t   * We use an Error-like object for backward compatibility as people may call\n\t   * PropTypes directly and inspect their output. However, we don't use real\n\t   * Errors anymore. We don't inspect their stack anyway, and creating them\n\t   * is prohibitively expensive if they are created too often, such as what\n\t   * happens in oneOfType() for any type before the one that matched.\n\t   */\n\t  function PropTypeError(message, data) {\n\t    this.message = message;\n\t    this.data = data && typeof data === 'object' ? data: {};\n\t    this.stack = '';\n\t  }\n\t  // Make `instanceof Error` still work for returned errors.\n\t  PropTypeError.prototype = Error.prototype;\n\n\t  function createChainableTypeChecker(validate) {\n\t    if (true) {\n\t      var manualPropTypeCallCache = {};\n\t      var manualPropTypeWarningCount = 0;\n\t    }\n\t    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n\t      componentName = componentName || ANONYMOUS;\n\t      propFullName = propFullName || propName;\n\n\t      if (secret !== ReactPropTypesSecret) {\n\t        if (throwOnDirectAccess) {\n\t          // New behavior only for users of `prop-types` package\n\t          var err = new Error(\n\t            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n\t            'Use `PropTypes.checkPropTypes()` to call them. ' +\n\t            'Read more at http://fb.me/use-check-prop-types'\n\t          );\n\t          err.name = 'Invariant Violation';\n\t          throw err;\n\t        } else if ( true && typeof console !== 'undefined') {\n\t          // Old behavior for people using React.PropTypes\n\t          var cacheKey = componentName + ':' + propName;\n\t          if (\n\t            !manualPropTypeCallCache[cacheKey] &&\n\t            // Avoid spamming the console because they are often not actionable except for lib authors\n\t            manualPropTypeWarningCount < 3\n\t          ) {\n\t            printWarning(\n\t              'You are manually calling a React.PropTypes validation ' +\n\t              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n\t              'and will throw in the standalone `prop-types` package. ' +\n\t              'You may be seeing this warning due to a third-party PropTypes ' +\n\t              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n\t            );\n\t            manualPropTypeCallCache[cacheKey] = true;\n\t            manualPropTypeWarningCount++;\n\t          }\n\t        }\n\t      }\n\t      if (props[propName] == null) {\n\t        if (isRequired) {\n\t          if (props[propName] === null) {\n\t            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n\t          }\n\t          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n\t        }\n\t        return null;\n\t      } else {\n\t        return validate(props, propName, componentName, location, propFullName);\n\t      }\n\t    }\n\n\t    var chainedCheckType = checkType.bind(null, false);\n\t    chainedCheckType.isRequired = checkType.bind(null, true);\n\n\t    return chainedCheckType;\n\t  }\n\n\t  function createPrimitiveTypeChecker(expectedType) {\n\t    function validate(props, propName, componentName, location, propFullName, secret) {\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== expectedType) {\n\t        // `propValue` being instance of, say, date/regexp, pass the 'object'\n\t        // check, but we can offer a more precise error message here rather than\n\t        // 'of type `object`'.\n\t        var preciseType = getPreciseType(propValue);\n\n\t        return new PropTypeError(\n\t          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n\t          {expectedType: expectedType}\n\t        );\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createAnyTypeChecker() {\n\t    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n\t  }\n\n\t  function createArrayOfTypeChecker(typeChecker) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (typeof typeChecker !== 'function') {\n\t        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n\t      }\n\t      var propValue = props[propName];\n\t      if (!Array.isArray(propValue)) {\n\t        var propType = getPropType(propValue);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n\t      }\n\t      for (var i = 0; i < propValue.length; i++) {\n\t        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n\t        if (error instanceof Error) {\n\t          return error;\n\t        }\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createElementTypeChecker() {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      if (!isValidElement(propValue)) {\n\t        var propType = getPropType(propValue);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createElementTypeTypeChecker() {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      if (!ReactIs.isValidElementType(propValue)) {\n\t        var propType = getPropType(propValue);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createInstanceTypeChecker(expectedClass) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (!(props[propName] instanceof expectedClass)) {\n\t        var expectedClassName = expectedClass.name || ANONYMOUS;\n\t        var actualClassName = getClassName(props[propName]);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createEnumTypeChecker(expectedValues) {\n\t    if (!Array.isArray(expectedValues)) {\n\t      if (true) {\n\t        if (arguments.length > 1) {\n\t          printWarning(\n\t            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n\t            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n\t          );\n\t        } else {\n\t          printWarning('Invalid argument supplied to oneOf, expected an array.');\n\t        }\n\t      }\n\t      return emptyFunctionThatReturnsNull;\n\t    }\n\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      for (var i = 0; i < expectedValues.length; i++) {\n\t        if (is(propValue, expectedValues[i])) {\n\t          return null;\n\t        }\n\t      }\n\n\t      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n\t        var type = getPreciseType(value);\n\t        if (type === 'symbol') {\n\t          return String(value);\n\t        }\n\t        return value;\n\t      });\n\t      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createObjectOfTypeChecker(typeChecker) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (typeof typeChecker !== 'function') {\n\t        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n\t      }\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== 'object') {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n\t      }\n\t      for (var key in propValue) {\n\t        if (has(propValue, key)) {\n\t          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\t          if (error instanceof Error) {\n\t            return error;\n\t          }\n\t        }\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createUnionTypeChecker(arrayOfTypeCheckers) {\n\t    if (!Array.isArray(arrayOfTypeCheckers)) {\n\t       true ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : 0;\n\t      return emptyFunctionThatReturnsNull;\n\t    }\n\n\t    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n\t      var checker = arrayOfTypeCheckers[i];\n\t      if (typeof checker !== 'function') {\n\t        printWarning(\n\t          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n\t          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n\t        );\n\t        return emptyFunctionThatReturnsNull;\n\t      }\n\t    }\n\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var expectedTypes = [];\n\t      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n\t        var checker = arrayOfTypeCheckers[i];\n\t        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n\t        if (checkerResult == null) {\n\t          return null;\n\t        }\n\t        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n\t          expectedTypes.push(checkerResult.data.expectedType);\n\t        }\n\t      }\n\t      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n\t      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createNodeChecker() {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (!isNode(props[propName])) {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function invalidValidatorError(componentName, location, propFullName, key, type) {\n\t    return new PropTypeError(\n\t      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n\t      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n\t    );\n\t  }\n\n\t  function createShapeTypeChecker(shapeTypes) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== 'object') {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n\t      }\n\t      for (var key in shapeTypes) {\n\t        var checker = shapeTypes[key];\n\t        if (typeof checker !== 'function') {\n\t          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n\t        }\n\t        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\t        if (error) {\n\t          return error;\n\t        }\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createStrictShapeTypeChecker(shapeTypes) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== 'object') {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n\t      }\n\t      // We need to check all keys in case some are required but missing from props.\n\t      var allKeys = assign({}, props[propName], shapeTypes);\n\t      for (var key in allKeys) {\n\t        var checker = shapeTypes[key];\n\t        if (has(shapeTypes, key) && typeof checker !== 'function') {\n\t          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n\t        }\n\t        if (!checker) {\n\t          return new PropTypeError(\n\t            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n\t            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n\t            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n\t          );\n\t        }\n\t        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\t        if (error) {\n\t          return error;\n\t        }\n\t      }\n\t      return null;\n\t    }\n\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function isNode(propValue) {\n\t    switch (typeof propValue) {\n\t      case 'number':\n\t      case 'string':\n\t      case 'undefined':\n\t        return true;\n\t      case 'boolean':\n\t        return !propValue;\n\t      case 'object':\n\t        if (Array.isArray(propValue)) {\n\t          return propValue.every(isNode);\n\t        }\n\t        if (propValue === null || isValidElement(propValue)) {\n\t          return true;\n\t        }\n\n\t        var iteratorFn = getIteratorFn(propValue);\n\t        if (iteratorFn) {\n\t          var iterator = iteratorFn.call(propValue);\n\t          var step;\n\t          if (iteratorFn !== propValue.entries) {\n\t            while (!(step = iterator.next()).done) {\n\t              if (!isNode(step.value)) {\n\t                return false;\n\t              }\n\t            }\n\t          } else {\n\t            // Iterator will provide entry [k,v] tuples rather than values.\n\t            while (!(step = iterator.next()).done) {\n\t              var entry = step.value;\n\t              if (entry) {\n\t                if (!isNode(entry[1])) {\n\t                  return false;\n\t                }\n\t              }\n\t            }\n\t          }\n\t        } else {\n\t          return false;\n\t        }\n\n\t        return true;\n\t      default:\n\t        return false;\n\t    }\n\t  }\n\n\t  function isSymbol(propType, propValue) {\n\t    // Native Symbol.\n\t    if (propType === 'symbol') {\n\t      return true;\n\t    }\n\n\t    // falsy value can't be a Symbol\n\t    if (!propValue) {\n\t      return false;\n\t    }\n\n\t    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n\t    if (propValue['@@toStringTag'] === 'Symbol') {\n\t      return true;\n\t    }\n\n\t    // Fallback for non-spec compliant Symbols which are polyfilled.\n\t    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n\t      return true;\n\t    }\n\n\t    return false;\n\t  }\n\n\t  // Equivalent of `typeof` but with special handling for array and regexp.\n\t  function getPropType(propValue) {\n\t    var propType = typeof propValue;\n\t    if (Array.isArray(propValue)) {\n\t      return 'array';\n\t    }\n\t    if (propValue instanceof RegExp) {\n\t      // Old webkits (at least until Android 4.0) return 'function' rather than\n\t      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n\t      // passes PropTypes.object.\n\t      return 'object';\n\t    }\n\t    if (isSymbol(propType, propValue)) {\n\t      return 'symbol';\n\t    }\n\t    return propType;\n\t  }\n\n\t  // This handles more types than `getPropType`. Only used for error messages.\n\t  // See `createPrimitiveTypeChecker`.\n\t  function getPreciseType(propValue) {\n\t    if (typeof propValue === 'undefined' || propValue === null) {\n\t      return '' + propValue;\n\t    }\n\t    var propType = getPropType(propValue);\n\t    if (propType === 'object') {\n\t      if (propValue instanceof Date) {\n\t        return 'date';\n\t      } else if (propValue instanceof RegExp) {\n\t        return 'regexp';\n\t      }\n\t    }\n\t    return propType;\n\t  }\n\n\t  // Returns a string that is postfixed to a warning about an invalid type.\n\t  // For example, \"undefined\" or \"of type array\"\n\t  function getPostfixForTypeWarning(value) {\n\t    var type = getPreciseType(value);\n\t    switch (type) {\n\t      case 'array':\n\t      case 'object':\n\t        return 'an ' + type;\n\t      case 'boolean':\n\t      case 'date':\n\t      case 'regexp':\n\t        return 'a ' + type;\n\t      default:\n\t        return type;\n\t    }\n\t  }\n\n\t  // Returns class name of the object, if any.\n\t  function getClassName(propValue) {\n\t    if (!propValue.constructor || !propValue.constructor.name) {\n\t      return ANONYMOUS;\n\t    }\n\t    return propValue.constructor.name;\n\t  }\n\n\t  ReactPropTypes.checkPropTypes = checkPropTypes;\n\t  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n\t  ReactPropTypes.PropTypes = ReactPropTypes;\n\n\t  return ReactPropTypes;\n\t};\n\treturn factoryWithTypeCheckers;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar factoryWithThrowingShims;\nvar hasRequiredFactoryWithThrowingShims;\n\nfunction requireFactoryWithThrowingShims () {\n\tif (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;\n\thasRequiredFactoryWithThrowingShims = 1;\n\n\tvar ReactPropTypesSecret = requireReactPropTypesSecret();\n\n\tfunction emptyFunction() {}\n\tfunction emptyFunctionWithReset() {}\n\temptyFunctionWithReset.resetWarningCache = emptyFunction;\n\n\tfactoryWithThrowingShims = function() {\n\t  function shim(props, propName, componentName, location, propFullName, secret) {\n\t    if (secret === ReactPropTypesSecret) {\n\t      // It is still safe when called from React.\n\t      return;\n\t    }\n\t    var err = new Error(\n\t      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n\t      'Use PropTypes.checkPropTypes() to call them. ' +\n\t      'Read more at http://fb.me/use-check-prop-types'\n\t    );\n\t    err.name = 'Invariant Violation';\n\t    throw err;\n\t  }\t  shim.isRequired = shim;\n\t  function getShim() {\n\t    return shim;\n\t  }\t  // Important!\n\t  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\t  var ReactPropTypes = {\n\t    array: shim,\n\t    bigint: shim,\n\t    bool: shim,\n\t    func: shim,\n\t    number: shim,\n\t    object: shim,\n\t    string: shim,\n\t    symbol: shim,\n\n\t    any: shim,\n\t    arrayOf: getShim,\n\t    element: shim,\n\t    elementType: shim,\n\t    instanceOf: getShim,\n\t    node: shim,\n\t    objectOf: getShim,\n\t    oneOf: getShim,\n\t    oneOfType: getShim,\n\t    shape: getShim,\n\t    exact: getShim,\n\n\t    checkPropTypes: emptyFunctionWithReset,\n\t    resetWarningCache: emptyFunction\n\t  };\n\n\t  ReactPropTypes.PropTypes = ReactPropTypes;\n\n\t  return ReactPropTypes;\n\t};\n\treturn factoryWithThrowingShims;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (true) {\n  var ReactIs = requireReactIs();\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  propTypes.exports = requireFactoryWithTypeCheckers()(ReactIs.isElement, throwOnDirectAccess);\n} else {}\n\nvar propTypesExports = propTypes.exports;\n\nconst MOBILE_WIDTH = 768;\r\nconst DEFAULT_PROPS = {\r\n    zoom: \"out\",\r\n    zoomScale: 0.5,\r\n    zoomTime: 10,\r\n    zoomTimingFunction: \"linear\",\r\n    zoomMax: 0.25,\r\n    transitionTime: 1,\r\n};\n\nconst useScreenSize = () => {\r\n    const [width, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(window.innerWidth);\r\n    const handleWindowSizeChange = () => {\r\n        setWidth(window.innerWidth);\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        window.addEventListener(\"resize\", handleWindowSizeChange);\r\n        return () => {\r\n            window.removeEventListener(\"resize\", handleWindowSizeChange);\r\n        };\r\n    }, []);\r\n    return width <= MOBILE_WIDTH;\r\n};\n\nfunction styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar css_248z = \".izf {\\n  position: relative;\\n  width: 100%;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n.izf__image {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  opacity: 0;\\n  visibility: hidden;\\n}\\n.izf__image--active {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n.izf__image--zoom-out {\\n  transform: scale(var(--izf-scale));\\n}\\n.izf__image--zoom-in {\\n  transform: scale(var(--izf-scale));\\n}\\n.izf__children {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  z-index: calc(var(--izf-max-z-index) + 1);\\n  width: 100%;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.35);\\n  opacity: 0;\\n  overflow: hidden;\\n  animation: izf-children-fade-in 1.25s ease forwards;\\n}\\n\\n@keyframes izf-children-fade-in {\\n  100% {\\n    opacity: 1;\\n  }\\n}\";\nstyleInject(css_248z);\n\nconst InfiniteZoomFader = (props) => {\r\n    var _a;\r\n    const { images, zoom, zoomTime, zoomTimingFunction, transitionTime, children, zoomMax, zoomScale, } = props;\r\n    if (!images)\r\n        return null;\r\n    const isMobile = useScreenSize();\r\n    const imageArray = isMobile ? images === null || images === void 0 ? void 0 : images.mobile : images === null || images === void 0 ? void 0 : images.desktop;\r\n    const [loadedCount, setLoadedCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(2);\r\n    const [previousIndex, setPreviousIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\r\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\r\n    const [switching, setSwitching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\r\n    const getVal = (val) => {\r\n        if (val < 0)\r\n            return 0;\r\n        if (val > 1)\r\n            return 1;\r\n        return val;\r\n    };\r\n    const max = getVal(zoomMax);\r\n    const scale = getVal(zoomScale);\r\n    const scaling = zoom === \"in\" ? 1 + max * scale : 1 + max - max * scale;\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        const timeout = setTimeout(() => {\r\n            setActiveIndex(0);\r\n        }, 1);\r\n        return () => clearTimeout(timeout);\r\n    }, []);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        const interval = setInterval(() => {\r\n            setActiveIndex((activeIndex) => activeIndex >= (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1 ? 0 : activeIndex + 1);\r\n            setSwitching(true);\r\n            setLoadedCount((loadedCount) => loadedCount >= (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length)\r\n                ? imageArray === null || imageArray === void 0 ? void 0 : imageArray.length\r\n                : (loadedCount += 1));\r\n            const timeout = setTimeout(() => {\r\n                setSwitching(false);\r\n            }, transitionTime * 1000);\r\n            return () => clearTimeout(timeout);\r\n        }, (zoomTime - transitionTime) * 1000);\r\n        return () => clearInterval(interval);\r\n    }, [zoomTime, transitionTime]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        setPreviousIndex(activeIndex - 1 < 0 ? (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1 : activeIndex - 1);\r\n    }, [activeIndex]);\r\n    const shouldAnimate = (index) => {\r\n        return (activeIndex === index ||\r\n            (activeIndex === index + 1 && switching) ||\r\n            (activeIndex === 0 &&\r\n                previousIndex === (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1 &&\r\n                switching &&\r\n                (index === 0 || index === (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1)));\r\n    };\r\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: \"izf\", style: {\r\n            [\"--izf-scale\"]: `${zoom === \"out\" ? 1 + getVal(zoomMax) : 1}`,\r\n            [\"--izf-max-z-index\"]: `${imageArray === null || imageArray === void 0 ? void 0 : imageArray.length}`,\r\n        } }, (_a = imageArray === null || imageArray === void 0 ? void 0 : imageArray.slice(0, loadedCount)) === null || _a === void 0 ? void 0 :\r\n        _a.map(({ src, alt }, index) => (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"img\", { className: `izf__image \n            ${shouldAnimate(index) ? \"izf__image--active \" : \"\"}\n            ${zoom === \"out\" && \"izf__image--zoom-out\"}\n            ${zoom === \"in\" && \"izf__image--zoom-in\"} \n          }`, style: shouldAnimate(index)\r\n                ? {\r\n                    transition: `opacity 0.67s ${zoomTimingFunction}, transform ${zoomTime}s ${zoomTimingFunction}`,\r\n                    transform: `scale(${scaling})`,\r\n                    zIndex: `${activeIndex === 0 &&\r\n                        previousIndex === (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1\r\n                        ? (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - index\r\n                        : \"\"}`,\r\n                }\r\n                : {}, src: src, key: `${src}-${index}`, alt: alt }))),\r\n        children && react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: `izf__children` }, children)));\r\n};\r\nInfiniteZoomFader.defaultProps = DEFAULT_PROPS;\r\nInfiniteZoomFader.propTypes = {\r\n    images: propTypesExports.shape({\r\n        desktop: propTypesExports.arrayOf(propTypesExports.shape({ src: propTypesExports.string, alt: propTypesExports.string })),\r\n        mobile: propTypesExports.arrayOf(propTypesExports.shape({ src: propTypesExports.string, alt: propTypesExports.string })),\r\n    }).isRequired,\r\n    zoom: propTypesExports.oneOf([\"in\", \"out\"]),\r\n    zoomMax: (props) => {\r\n        if (props.zoomScale < 0 || props.zoomScale > 1) {\r\n            return new Error(\"Invalid value supplied as zoomMax: must be a value between 0 and 1\");\r\n        }\r\n    },\r\n    zoomScale: (props) => {\r\n        if (props.zoomScale < 0 || props.zoomScale > 1) {\r\n            return new Error(\"Invalid value supplied as zoomScale: must be a value between 0 and 1\");\r\n        }\r\n    },\r\n    zoomTime: propTypesExports.number,\r\n    zoomTimingFunction: propTypesExports.oneOf([\r\n        \"linear\",\r\n        \"ease\",\r\n        \"ease-in\",\r\n        \"ease-out\",\r\n        \"ease-in-out\",\r\n        RegExp(/cubic-bezier\\\\(([0-9]*\\\\.?[0-9]+),\\\\s*([0-9]*\\\\.?[0-9]+),\\\\s*([0-9]*\\\\.?[0-9]+),\\\\s*([0-9]*\\\\.?[0-9]+)\\\\)/),\r\n    ]),\r\n    transitionTime: propTypesExports.number,\r\n};\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/infinite-zoom-fader/dist/esm/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CrystalApp/HeroBanner.tsx":
/*!**************************************************!*\
  !*** ./src/components/CrystalApp/HeroBanner.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @styled-icons/material-sharp/ContactSupport */ \"(app-pages-browser)/./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fslightbox-react */ \"(app-pages-browser)/./node_modules/fslightbox-react/index.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fslightbox_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! infinite-zoom-fader */ \"(app-pages-browser)/./node_modules/infinite-zoom-fader/dist/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var styled_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! styled-icons/material */ \"(app-pages-browser)/./node_modules/@styled-icons/material/Quiz/Quiz.esm.js\");\n/* harmony import */ var _QuizModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./QuizModal */ \"(app-pages-browser)/./src/components/CrystalApp/QuizModal.tsx\");\n/* harmony import */ var styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! styled-icons/heroicons-outline */ \"(app-pages-browser)/./node_modules/@styled-icons/heroicons-outline/BookOpen/BookOpen.esm.js\");\n/* harmony import */ var styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! styled-icons/ionicons-outline */ \"(app-pages-browser)/./node_modules/@styled-icons/ionicons-outline/Play/Play.esm.js\");\n/* harmony import */ var _services_SliderService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/SliderService */ \"(app-pages-browser)/./src/services/SliderService.ts\");\n/* harmony import */ var _services_HeroService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/HeroService */ \"(app-pages-browser)/./src/services/HeroService.ts\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HeroBanner = ()=>{\n    _s();\n    // To open the lightbox change the value of the \"toggler\" prop.\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const [toggler, setToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [brochureToggler, setBrochureToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isQuizModalOpen, setIsQuizModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [heroData, setHeroData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [languageId, setLanguageId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const [sliderText, setSliderText] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [imagesLoading, setImagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [sliderData, setSliderData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            setImagesLoading(true);\n            try {\n                const response = await (0,_services_SliderService__WEBPACK_IMPORTED_MODULE_7__.getSlider)();\n                if (response.data && response.data.Data !== sliderData) {\n                    console.log(\"response.data.Data\", response.data.Data);\n                    setSliderData(response.data.Data); // Only update if data has changed\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch slider data:\", error);\n            } finally{\n                setImagesLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const handleLanguageChange = ()=>{\n            const languageCode = i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].language || localStorage.getItem(\"i18nextLng\");\n            setLanguageId(languageCode === \"tr\" ? 1 : 2);\n        };\n        handleLanguageChange();\n        i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].on(\"languageChanged\", handleLanguageChange);\n        return ()=>{\n            i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].off(\"languageChanged\", handleLanguageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        async function fetchData() {\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroSliderText)(languageId);\n                if (response.data) {\n                    setSliderText(response.data.Data);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch FAQs:\", error);\n            }\n        }\n        fetchData();\n    }, [\n        languageId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const getHeroData = async ()=>{\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroBanner)();\n                setHeroData(response.data.Data);\n            } catch (error) {\n                console.error(\"Failed to fetch hero data:\", error);\n            }\n        };\n        getHeroData();\n    }, []);\n    const responseImages = {\n        desktop: sliderData.map((item)=>{\n            return {\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            };\n        }),\n        mobile: sliderData.map((item)=>({\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            }))\n    };\n    /*   const images = {\r\n    desktop: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n      {\r\n        src: sliderData[4]?.Image ? sliderData[4]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[5]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[6]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[7]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[8]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n    ],\r\n    mobile: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n    ],\r\n  }; */ // change title and subtitle with the images, in every 5 seconds\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.title\"));\n    const [subtitle, setSubtitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.subtitle\"));\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            switch(index){\n                case 0:\n                    var _sliderText_, _sliderText_1, _sliderText_2, _sliderText_3;\n                    setTitle(((_sliderText_ = sliderText[0]) === null || _sliderText_ === void 0 ? void 0 : _sliderText_.Title) ? (_sliderText_1 = sliderText[0]) === null || _sliderText_1 === void 0 ? void 0 : _sliderText_1.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_2 = sliderText[0]) === null || _sliderText_2 === void 0 ? void 0 : _sliderText_2.Content) ? (_sliderText_3 = sliderText[0]) === null || _sliderText_3 === void 0 ? void 0 : _sliderText_3.Content : t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n                case 1:\n                    var _sliderText_4, _sliderText_5, _sliderText_6, _sliderText_7;\n                    setTitle(((_sliderText_4 = sliderText[1]) === null || _sliderText_4 === void 0 ? void 0 : _sliderText_4.Title) ? (_sliderText_5 = sliderText[1]) === null || _sliderText_5 === void 0 ? void 0 : _sliderText_5.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_6 = sliderText[1]) === null || _sliderText_6 === void 0 ? void 0 : _sliderText_6.Content) ? (_sliderText_7 = sliderText[1]) === null || _sliderText_7 === void 0 ? void 0 : _sliderText_7.Content : t(\"hero.subtitle2\"));\n                    setIndex(2);\n                    break;\n                case 2:\n                    var _sliderText_8, _sliderText_9, _sliderText_10, _sliderText_11;\n                    setTitle(((_sliderText_8 = sliderText[2]) === null || _sliderText_8 === void 0 ? void 0 : _sliderText_8.Title) ? (_sliderText_9 = sliderText[2]) === null || _sliderText_9 === void 0 ? void 0 : _sliderText_9.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_10 = sliderText[2]) === null || _sliderText_10 === void 0 ? void 0 : _sliderText_10.Content) ? (_sliderText_11 = sliderText[2]) === null || _sliderText_11 === void 0 ? void 0 : _sliderText_11.Content : t(\"hero.subtitle3\"));\n                    setIndex(0);\n                    break;\n                case 3:\n                    var _sliderText_12, _sliderText_13, _sliderText_14, _sliderText_15;\n                    setTitle(((_sliderText_12 = sliderText[3]) === null || _sliderText_12 === void 0 ? void 0 : _sliderText_12.Title) ? (_sliderText_13 = sliderText[3]) === null || _sliderText_13 === void 0 ? void 0 : _sliderText_13.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_14 = sliderText[3]) === null || _sliderText_14 === void 0 ? void 0 : _sliderText_14.Content) ? (_sliderText_15 = sliderText[3]) === null || _sliderText_15 === void 0 ? void 0 : _sliderText_15.Content : t(\"hero.subtitle4\"));\n                    setIndex(0);\n                    break;\n                default:\n                    setTitle(t(\"hero.title\"));\n                    setSubtitle(t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n            }\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, [\n        index,\n        t,\n        sliderText\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[95vh] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuizModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isQuizModalOpen,\n                setIsOpen: setIsQuizModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: toggler,\n                sources: [\n                    heroData === null || heroData === void 0 ? void 0 : heroData.VideoLink\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: brochureToggler,\n                sources: [\n                    heroData === null || heroData === void 0 ? void 0 : heroData.BrochureLink\n                ],\n                type: \"image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            !imagesLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__.InfiniteZoomFader, {\n                images: responseImages,\n                zoom: \"out\",\n                zoomScale: 0.75,\n                zoomTime: 5,\n                zoomMax: 0.25,\n                zoomTimingFunction: \"linear\",\n                transitionTime: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 w-full h-full absolute top-0 left-0 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"home\",\n                        className: \"container max-w-[1760px] xl:px-[30px] h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-[25px] items-center grid-cols-1 md:grid-cols-1 lg:grid-cols-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-[30px] md:space-y-[40px] lg:space-y-[30px] xl:space-y-[40px] lg:max-w-[590px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-h-[200px] md:min-h-[300px] \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \" text-[35px] md:text-[42px] lg:text-[40px] xl:text-[50px] 2xl:text-[55px] leading-[35px] md:leading-[46px] lg:leading-[55px] xl:leading-[64px] mb-[20px] md:mb-[30px] lg:mb-[20px] xl:mb-[40px] text-white\",\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"100\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[16px] md:text-[18px] text-stone-300\",\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"200\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:flex items-center space-y-[20px] sm:space-y-[0] sm:space-x-[30px]\",\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"300\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-row  gap-[30px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"#contact\",\n                                                        className: \"inline-block text-white font-semibold text-[16px] md:text-[18px] transition duration-500 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__.ContactSupport, {\n                                                                size: 48,\n                                                                className: \"mr-[3px] mb-[2px]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            t(\"hero.contact\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                        onClick: ()=>setToggler(!toggler),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-[#fff] w-[36px] h-[36px] leading-[36px] pl-[5px] rounded-full text-center text-[22px] transition duration-500 ease-in-out group-hover:bg-primary-color group-hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__.Play, {\n                                                                    size: 28,\n                                                                    className: \"mr-[3px] mb-[2px]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-[16px] md:text-[18px] text-white\",\n                                                                children: t(\"hero.howItWorks\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                onClick: ()=>setBrochureToggler(!brochureToggler),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-[16px] md:text-[18px] text-sky-300 hover:text-sky-400 md:-ml-2 uppercase underline underline-offset-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__.BookOpen, {\n                                                            size: 20,\n                                                            className: \"mr-[6px] mb-[2px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        t(\"hero.brochure\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsQuizModalOpen(true),\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"400\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        className: \" text-stone-200 font-semibold text-[16px] md:text-[18px] text-left italic underline underline-offset-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_material__WEBPACK_IMPORTED_MODULE_13__.Quiz, {\n                                                size: 28,\n                                                className: \"mr-[2px] inline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            t(\"hero.test\"),\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeroBanner, \"KvSuoIr/C5odsSHWEYLLWsDoVEg=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = HeroBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HeroBanner);\nvar _c;\n$RefreshReg$(_c, \"HeroBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NyeXN0YWxBcHAvSGVyb0Jhbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNkU7QUFDbkM7QUFDYztBQUMzQjtBQUNlO0FBQ0c7QUFFRjtBQUNUO0FBQ3NCO0FBQ0w7QUFDQTtBQUNxQjtBQUM1QztBQUU5QixNQUFNZSxhQUFhOztJQUNqQiwrREFBK0Q7SUFDL0QsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR1YsNkRBQWNBO0lBQzVCLE1BQU0sQ0FBQ1csU0FBU0MsV0FBVyxHQUFHYiwrQ0FBUUEsQ0FBVTtJQUNoRCxNQUFNLENBQUNjLGlCQUFpQkMsbUJBQW1CLEdBQUdmLCtDQUFRQSxDQUFVO0lBQ2hFLE1BQU0sQ0FBQ2dCLGlCQUFpQkMsbUJBQW1CLEdBQUdqQiwrQ0FBUUEsQ0FBVTtJQUNoRSxNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FBTSxFQUFFO0lBQ2hELE1BQU0sQ0FBQ29CLFlBQVlDLGNBQWMsR0FBR3JCLCtDQUFRQSxDQUFTO0lBQ3JELE1BQU0sQ0FBQ3NCLFlBQVlDLGNBQWMsR0FBR3ZCLCtDQUFRQSxDQUFNLEVBQUU7SUFDcEQsTUFBTSxDQUFDd0IsZUFBZUMsaUJBQWlCLEdBQUd6QiwrQ0FBUUEsQ0FBVTtJQUM1RCxNQUFNLENBQUMwQixZQUFZQyxjQUFjLEdBQUczQiwrQ0FBUUEsQ0FBUSxFQUFFO0lBRXRERCxnREFBU0EsQ0FBQztRQUNSLE1BQU02QixZQUFZO1lBQ2hCSCxpQkFBaUI7WUFDakIsSUFBSTtnQkFDRixNQUFNSSxXQUFXLE1BQU12QixrRUFBU0E7Z0JBQ2hDLElBQUl1QixTQUFTQyxJQUFJLElBQUlELFNBQVNDLElBQUksQ0FBQ0MsSUFBSSxLQUFLTCxZQUFZO29CQUN2RE0sUUFBUUMsR0FBRyxDQUFDLHNCQUFzQkosU0FBU0MsSUFBSSxDQUFDQyxJQUFJO29CQUNuREosY0FBY0UsU0FBU0MsSUFBSSxDQUFDQyxJQUFJLEdBQUcsa0NBQWtDO2dCQUN2RTtZQUNGLEVBQUUsT0FBT0csT0FBTztnQkFDZEYsUUFBUUUsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDaEQsU0FBVTtnQkFDUlQsaUJBQWlCO1lBQ25CO1FBQ0Y7UUFFQUc7SUFDRixHQUFHLEVBQUU7SUFFTDdCLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTW9DLHVCQUF1QjtZQUMzQixNQUFNQyxlQUNKM0IsK0NBQU9BLENBQUM0QixRQUFRLElBQUlDLGFBQWFDLE9BQU8sQ0FBQztZQUMzQ2xCLGNBQWNlLGlCQUFpQixPQUFPLElBQUk7UUFDNUM7UUFFQUQ7UUFFQTFCLCtDQUFPQSxDQUFDK0IsRUFBRSxDQUFDLG1CQUFtQkw7UUFFOUIsT0FBTztZQUNMMUIsK0NBQU9BLENBQUNnQyxHQUFHLENBQUMsbUJBQW1CTjtRQUNqQztJQUNGLEdBQUcsRUFBRTtJQUVMcEMsZ0RBQVNBLENBQUM7UUFDUixlQUFlNkI7WUFDYixJQUFJO2dCQUNGLE1BQU1DLFdBQVcsTUFBTXJCLHdFQUFpQkEsQ0FBQ1k7Z0JBQ3pDLElBQUlTLFNBQVNDLElBQUksRUFBRTtvQkFDakJQLGNBQWNNLFNBQVNDLElBQUksQ0FBQ0MsSUFBSTtnQkFDbEM7WUFDRixFQUFFLE9BQU9HLE9BQU87Z0JBQ2RGLFFBQVFFLEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3pDO1FBQ0Y7UUFDQU47SUFDRixHQUFHO1FBQUNSO0tBQVc7SUFFZnJCLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTTJDLGNBQWM7WUFDbEIsSUFBSTtnQkFDRixNQUFNYixXQUFXLE1BQU10QixvRUFBYUE7Z0JBQ3BDWSxZQUFZVSxTQUFTQyxJQUFJLENBQUNDLElBQUk7WUFDaEMsRUFBRSxPQUFPRyxPQUFPO2dCQUNkRixRQUFRRSxLQUFLLENBQUMsOEJBQThCQTtZQUM5QztRQUNGO1FBQ0FRO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUMsaUJBQWlCO1FBQ3JCQyxTQUFTbEIsV0FBV21CLEdBQUcsQ0FBQyxDQUFDQztZQUN2QixPQUFPO2dCQUNMQyxLQUFLRCxLQUFLRSxLQUFLO2dCQUNmQyxLQUFLSCxLQUFLSSxFQUFFLENBQUNDLFFBQVEsS0FBSztZQUM1QjtRQUNGO1FBQ0FDLFFBQVExQixXQUFXbUIsR0FBRyxDQUFDLENBQUNDLE9BQVU7Z0JBQ2hDQyxLQUFLRCxLQUFLRSxLQUFLO2dCQUNmQyxLQUFLSCxLQUFLSSxFQUFFLENBQUNDLFFBQVEsS0FBSztZQUM1QjtJQUNGO0lBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztLQXlERyxHQUVILGdFQUFnRTtJQUNoRSxNQUFNLENBQUNFLE9BQU9DLFNBQVMsR0FBR3RELCtDQUFRQSxDQUFTVyxFQUFFO0lBQzdDLE1BQU0sQ0FBQzRDLFVBQVVDLFlBQVksR0FBR3hELCtDQUFRQSxDQUFTVyxFQUFFO0lBRW5ELE1BQU0sQ0FBQzhDLE9BQU9DLFNBQVMsR0FBRzFELCtDQUFRQSxDQUFTO0lBRTNDRCxnREFBU0EsQ0FBQztRQUNSLE1BQU00RCxXQUFXQyxZQUFZO1lBQzNCLE9BQVFIO2dCQUNOLEtBQUs7d0JBRURuQyxjQUF1QkEsZUFHdkJBLGVBQXlCQTtvQkFKM0JnQyxTQUNFaEMsRUFBQUEsZUFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEsbUNBQUFBLGFBQWV1QyxLQUFLLEtBQUd2QyxnQkFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEsb0NBQUFBLGNBQWV1QyxLQUFLLEdBQUdsRCxFQUFFO29CQUVsRDZDLFlBQ0VsQyxFQUFBQSxnQkFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEsb0NBQUFBLGNBQWV3QyxPQUFPLEtBQUd4QyxnQkFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEsb0NBQUFBLGNBQWV3QyxPQUFPLEdBQUduRCxFQUFFO29CQUV0RCtDLFNBQVM7b0JBQ1Q7Z0JBQ0YsS0FBSzt3QkFFRHBDLGVBQXVCQSxlQUd2QkEsZUFDSUE7b0JBTE5nQyxTQUNFaEMsRUFBQUEsZ0JBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLG9DQUFBQSxjQUFldUMsS0FBSyxLQUFHdkMsZ0JBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLG9DQUFBQSxjQUFldUMsS0FBSyxHQUFHbEQsRUFBRTtvQkFFbEQ2QyxZQUNFbEMsRUFBQUEsZ0JBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLG9DQUFBQSxjQUFld0MsT0FBTyxLQUNsQnhDLGdCQUFBQSxVQUFVLENBQUMsRUFBRSxjQUFiQSxvQ0FBQUEsY0FBZXdDLE9BQU8sR0FDdEJuRCxFQUFFO29CQUVSK0MsU0FBUztvQkFDVDtnQkFDRixLQUFLO3dCQUVEcEMsZUFBdUJBLGVBR3ZCQSxnQkFDSUE7b0JBTE5nQyxTQUNFaEMsRUFBQUEsZ0JBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLG9DQUFBQSxjQUFldUMsS0FBSyxLQUFHdkMsZ0JBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLG9DQUFBQSxjQUFldUMsS0FBSyxHQUFHbEQsRUFBRTtvQkFFbEQ2QyxZQUNFbEMsRUFBQUEsaUJBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLHFDQUFBQSxlQUFld0MsT0FBTyxLQUNsQnhDLGlCQUFBQSxVQUFVLENBQUMsRUFBRSxjQUFiQSxxQ0FBQUEsZUFBZXdDLE9BQU8sR0FDdEJuRCxFQUFFO29CQUVSK0MsU0FBUztvQkFDVDtnQkFDRixLQUFLO3dCQUVEcEMsZ0JBQXVCQSxnQkFHdkJBLGdCQUNJQTtvQkFMTmdDLFNBQ0VoQyxFQUFBQSxpQkFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEscUNBQUFBLGVBQWV1QyxLQUFLLEtBQUd2QyxpQkFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEscUNBQUFBLGVBQWV1QyxLQUFLLEdBQUdsRCxFQUFFO29CQUVsRDZDLFlBQ0VsQyxFQUFBQSxpQkFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEscUNBQUFBLGVBQWV3QyxPQUFPLEtBQ2xCeEMsaUJBQUFBLFVBQVUsQ0FBQyxFQUFFLGNBQWJBLHFDQUFBQSxlQUFld0MsT0FBTyxHQUN0Qm5ELEVBQUU7b0JBRVIrQyxTQUFTO29CQUNUO2dCQUNGO29CQUNFSixTQUFTM0MsRUFBRTtvQkFDWDZDLFlBQVk3QyxFQUFFO29CQUNkK0MsU0FBUztvQkFDVDtZQUNKO1FBQ0YsR0FBRztRQUVILE9BQU8sSUFBTUssY0FBY0o7SUFDN0IsR0FBRztRQUFDRjtRQUFPOUM7UUFBR1c7S0FBVztJQUV6QixxQkFDRSw4REFBQzBDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDOUQsa0RBQVNBO2dCQUFDK0QsUUFBUWxEO2dCQUFpQm1ELFdBQVdsRDs7Ozs7OzBCQUMvQyw4REFBQ3JCLHlEQUFVQTtnQkFBQ2dCLFNBQVNBO2dCQUFTd0QsU0FBUztvQkFBQ2xELHFCQUFBQSwrQkFBQUEsU0FBVW1ELFNBQVM7aUJBQUM7Ozs7OzswQkFDNUQsOERBQUN6RSx5REFBVUE7Z0JBQ1RnQixTQUFTRTtnQkFDVHNELFNBQVM7b0JBQUNsRCxxQkFBQUEsK0JBQUFBLFNBQVVvRCxZQUFZO2lCQUFDO2dCQUNqQ0MsTUFBTTs7Ozs7O1lBRVAsQ0FBQy9DLCtCQUNBLDhEQUFDM0Isa0VBQWlCQTtnQkFDaEIyRSxRQUFRN0I7Z0JBQ1I4QixNQUFLO2dCQUNMQyxXQUFXO2dCQUNYQyxVQUFVO2dCQUNWQyxTQUFTO2dCQUNUQyxvQkFBbUI7Z0JBQ25CQyxnQkFBZ0I7O2tDQUVoQiw4REFBQ2Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQ0NlLElBQUc7d0JBQ0hkLFdBQVU7a0NBRVYsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2U7Z0RBQ0NmLFdBQVk7Z0RBQ1pnQixZQUFTO2dEQUNUQyxrQkFBZTtnREFDZkMscUJBQWtCO2dEQUNsQkMsaUJBQWM7MERBRWIvQjs7Ozs7OzBEQUdILDhEQUFDZ0M7Z0RBQ0NwQixXQUFZO2dEQUNaZ0IsWUFBUztnREFDVEMsa0JBQWU7Z0RBQ2ZDLHFCQUFrQjtnREFDbEJDLGlCQUFjOzBEQUViN0I7Ozs7Ozs7Ozs7OztrREFJTCw4REFBQ1M7d0NBQ0NDLFdBQVU7d0NBQ1ZnQixZQUFTO3dDQUNUQyxrQkFBZTt3Q0FDZkMscUJBQWtCO3dDQUNsQkMsaUJBQWM7OzBEQUVkLDhEQUFDcEI7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbkUsa0RBQUlBO3dEQUNId0YsTUFBSzt3REFDTHJCLFdBQVU7OzBFQUVWLDhEQUFDdEUsd0ZBQWNBO2dFQUFDNEYsTUFBTTtnRUFBSXRCLFdBQVU7Ozs7Ozs0REFDbkN0RCxFQUFFOzs7Ozs7O2tFQUdMLDhEQUFDcUQ7d0RBQ0NDLFdBQVU7d0RBQ1Z1QixTQUFTLElBQU0zRSxXQUFXLENBQUNEOzswRUFFM0IsOERBQUNvRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQzVELGdFQUFJQTtvRUFBQ2tGLE1BQU07b0VBQUl0QixXQUFVOzs7Ozs7Ozs7OzswRUFFNUIsOERBQUNvQjtnRUFBRXBCLFdBQVU7MEVBQ1Z0RCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSVQsOERBQUNxRDtnREFDQ0MsV0FBVTtnREFDVnVCLFNBQVMsSUFBTXpFLG1CQUFtQixDQUFDRDswREFFbkMsNEVBQUN1RTtvREFBRXBCLFdBQVU7O3NFQUNYLDhEQUFDN0QscUVBQVFBOzREQUFDbUYsTUFBTTs0REFBSXRCLFdBQVU7Ozs7Ozt3REFDN0J0RCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSVQsOERBQUM4RTt3Q0FDQ0QsU0FBUyxJQUFNdkUsbUJBQW1CO3dDQUNsQ2dFLFlBQVM7d0NBQ1RDLGtCQUFlO3dDQUNmQyxxQkFBa0I7d0NBQ2xCQyxpQkFBYzt3Q0FDZG5CLFdBQVU7OzBEQUVWLDhEQUFDL0Qsd0RBQUlBO2dEQUFDcUYsTUFBTTtnREFBSXRCLFdBQVU7Ozs7Ozs0Q0FDekJ0RCxFQUFFOzRDQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNsQztHQXZUTUQ7O1FBRVVULHlEQUFjQTs7O0tBRnhCUztBQXlUTiwrREFBZUEsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9DcnlzdGFsQXBwL0hlcm9CYW5uZXIudHN4PzEyYTkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgeyBDb250YWN0U3VwcG9ydCB9IGZyb20gXCJAc3R5bGVkLWljb25zL21hdGVyaWFsLXNoYXJwL0NvbnRhY3RTdXBwb3J0XCI7XHJcbmltcG9ydCBGc0xpZ2h0Ym94IGZyb20gXCJmc2xpZ2h0Ym94LXJlYWN0XCI7XHJcbmltcG9ydCB7IEluZmluaXRlWm9vbUZhZGVyIH0gZnJvbSBcImluZmluaXRlLXpvb20tZmFkZXJcIjtcclxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSBcInJlYWN0LWkxOG5leHRcIjtcclxuaW1wb3J0IHsgQm9va1JlYWRlciB9IGZyb20gXCJzdHlsZWQtaWNvbnMvYm94aWNvbnMtc29saWRcIjtcclxuaW1wb3J0IHsgUXVpeiB9IGZyb20gXCJzdHlsZWQtaWNvbnMvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IFF1aXpNb2RhbCBmcm9tIFwiLi9RdWl6TW9kYWxcIjtcclxuaW1wb3J0IHsgQm9va09wZW4gfSBmcm9tIFwic3R5bGVkLWljb25zL2hlcm9pY29ucy1vdXRsaW5lXCI7XHJcbmltcG9ydCB7IFBsYXkgfSBmcm9tIFwic3R5bGVkLWljb25zL2lvbmljb25zLW91dGxpbmVcIjtcclxuaW1wb3J0IHsgZ2V0U2xpZGVyIH0gZnJvbSBcIkAvc2VydmljZXMvU2xpZGVyU2VydmljZVwiO1xyXG5pbXBvcnQgeyBnZXRIZXJvQmFubmVyLCBnZXRIZXJvU2xpZGVyVGV4dCB9IGZyb20gXCJAL3NlcnZpY2VzL0hlcm9TZXJ2aWNlXCI7XHJcbmltcG9ydCBpMThuZXh0IGZyb20gXCJpMThuZXh0XCI7XHJcblxyXG5jb25zdCBIZXJvQmFubmVyID0gKCkgPT4ge1xyXG4gIC8vIFRvIG9wZW4gdGhlIGxpZ2h0Ym94IGNoYW5nZSB0aGUgdmFsdWUgb2YgdGhlIFwidG9nZ2xlclwiIHByb3AuXHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IFt0b2dnbGVyLCBzZXRUb2dnbGVyXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcclxuICBjb25zdCBbYnJvY2h1cmVUb2dnbGVyLCBzZXRCcm9jaHVyZVRvZ2dsZXJdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xyXG4gIGNvbnN0IFtpc1F1aXpNb2RhbE9wZW4sIHNldElzUXVpek1vZGFsT3Blbl0gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XHJcbiAgY29uc3QgW2hlcm9EYXRhLCBzZXRIZXJvRGF0YV0gPSB1c2VTdGF0ZTxhbnk+KFtdKTtcclxuICBjb25zdCBbbGFuZ3VhZ2VJZCwgc2V0TGFuZ3VhZ2VJZF0gPSB1c2VTdGF0ZTxudW1iZXI+KDEpO1xyXG4gIGNvbnN0IFtzbGlkZXJUZXh0LCBzZXRTbGlkZXJUZXh0XSA9IHVzZVN0YXRlPGFueT4oW10pO1xyXG4gIGNvbnN0IFtpbWFnZXNMb2FkaW5nLCBzZXRJbWFnZXNMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KHRydWUpO1xyXG4gIGNvbnN0IFtzbGlkZXJEYXRhLCBzZXRTbGlkZXJEYXRhXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBmZXRjaERhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHNldEltYWdlc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRTbGlkZXIoKTtcclxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLkRhdGEgIT09IHNsaWRlckRhdGEpIHtcclxuICAgICAgICAgY29uc29sZS5sb2coXCJyZXNwb25zZS5kYXRhLkRhdGFcIiwgcmVzcG9uc2UuZGF0YS5EYXRhKVxyXG4gICAgICAgICAgc2V0U2xpZGVyRGF0YShyZXNwb25zZS5kYXRhLkRhdGEpOyAvLyBPbmx5IHVwZGF0ZSBpZiBkYXRhIGhhcyBjaGFuZ2VkXHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggc2xpZGVyIGRhdGE6XCIsIGVycm9yKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRJbWFnZXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBmZXRjaERhdGEoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVMYW5ndWFnZUNoYW5nZSA9ICgpID0+IHtcclxuICAgICAgY29uc3QgbGFuZ3VhZ2VDb2RlID1cclxuICAgICAgICBpMThuZXh0Lmxhbmd1YWdlIHx8IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiaTE4bmV4dExuZ1wiKTtcclxuICAgICAgc2V0TGFuZ3VhZ2VJZChsYW5ndWFnZUNvZGUgPT09IFwidHJcIiA/IDEgOiAyKTtcclxuICAgIH07XHJcblxyXG4gICAgaGFuZGxlTGFuZ3VhZ2VDaGFuZ2UoKTtcclxuXHJcbiAgICBpMThuZXh0Lm9uKFwibGFuZ3VhZ2VDaGFuZ2VkXCIsIGhhbmRsZUxhbmd1YWdlQ2hhbmdlKTtcclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBpMThuZXh0Lm9mZihcImxhbmd1YWdlQ2hhbmdlZFwiLCBoYW5kbGVMYW5ndWFnZUNoYW5nZSk7XHJcbiAgICB9O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGFzeW5jIGZ1bmN0aW9uIGZldGNoRGF0YSgpIHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldEhlcm9TbGlkZXJUZXh0KGxhbmd1YWdlSWQpO1xyXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgICAgICBzZXRTbGlkZXJUZXh0KHJlc3BvbnNlLmRhdGEuRGF0YSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggRkFRczpcIiwgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBmZXRjaERhdGEoKTtcclxuICB9LCBbbGFuZ3VhZ2VJZF0pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgZ2V0SGVyb0RhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRIZXJvQmFubmVyKCk7XHJcbiAgICAgICAgc2V0SGVyb0RhdGEocmVzcG9uc2UuZGF0YS5EYXRhKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIGhlcm8gZGF0YTpcIiwgZXJyb3IpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gICAgZ2V0SGVyb0RhdGEoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIGNvbnN0IHJlc3BvbnNlSW1hZ2VzID0ge1xyXG4gICAgZGVza3RvcDogc2xpZGVyRGF0YS5tYXAoKGl0ZW0pID0+IHtcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzcmM6IGl0ZW0uSW1hZ2UsXHJcbiAgICAgICAgYWx0OiBpdGVtLklkLnRvU3RyaW5nKCkgKyBcIiBhbHRcIixcclxuICAgICAgfTtcclxuICAgIH0pLFxyXG4gICAgbW9iaWxlOiBzbGlkZXJEYXRhLm1hcCgoaXRlbSkgPT4gKHtcclxuICAgICAgc3JjOiBpdGVtLkltYWdlLFxyXG4gICAgICBhbHQ6IGl0ZW0uSWQudG9TdHJpbmcoKSArIFwiIGFsdFwiLFxyXG4gICAgfSkpLFxyXG4gIH07XHJcblxyXG4gIC8qICAgY29uc3QgaW1hZ2VzID0ge1xyXG4gICAgZGVza3RvcDogW1xyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiBzbGlkZXJEYXRhWzBdPy5JbWFnZSxcclxuICAgICAgICBhbHQ6IFwiSW1hZ2UgMSBhbHRcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIHNyYzogc2xpZGVyRGF0YVsxXT8uSW1hZ2UsXHJcbiAgICAgICAgYWx0OiBcIkltYWdlIDIgYWx0XCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBzcmM6IHNsaWRlckRhdGFbMl0/LkltYWdlLFxyXG4gICAgICAgIGFsdDogXCJJbWFnZSAzIGFsdFwiLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiBzbGlkZXJEYXRhWzNdPy5JbWFnZSxcclxuICAgICAgICBhbHQ6IFwiSW1hZ2UgNCBhbHRcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIHNyYzogc2xpZGVyRGF0YVs0XT8uSW1hZ2UgPyBzbGlkZXJEYXRhWzRdPy5JbWFnZSA6IHNsaWRlckRhdGFbMF0/LkltYWdlLFxyXG4gICAgICAgIGFsdDogXCJJbWFnZSA1IGFsdFwiLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiBzbGlkZXJEYXRhWzVdPy5JbWFnZSA/IHNsaWRlckRhdGFbNV0/LkltYWdlIDogc2xpZGVyRGF0YVswXT8uSW1hZ2UsXHJcbiAgICAgICAgYWx0OiBcIkltYWdlIDUgYWx0XCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBzcmM6IHNsaWRlckRhdGFbNl0/LkltYWdlID8gc2xpZGVyRGF0YVs1XT8uSW1hZ2UgOiBzbGlkZXJEYXRhWzBdPy5JbWFnZSxcclxuICAgICAgICBhbHQ6IFwiSW1hZ2UgNSBhbHRcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIHNyYzogc2xpZGVyRGF0YVs3XT8uSW1hZ2UgPyBzbGlkZXJEYXRhWzVdPy5JbWFnZSA6IHNsaWRlckRhdGFbMF0/LkltYWdlLFxyXG4gICAgICAgIGFsdDogXCJJbWFnZSA1IGFsdFwiLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiBzbGlkZXJEYXRhWzhdPy5JbWFnZSA/IHNsaWRlckRhdGFbNV0/LkltYWdlIDogc2xpZGVyRGF0YVswXT8uSW1hZ2UsXHJcbiAgICAgICAgYWx0OiBcIkltYWdlIDUgYWx0XCIsXHJcbiAgICAgIH0sXHJcbiAgICBdLFxyXG4gICAgbW9iaWxlOiBbXHJcbiAgICAgIHtcclxuICAgICAgICBzcmM6IHNsaWRlckRhdGFbMF0/LkltYWdlLFxyXG4gICAgICAgIGFsdDogXCJJbWFnZSAxIGFsdFwiLFxyXG4gICAgICB9LFxyXG4gICAgICB7XHJcbiAgICAgICAgc3JjOiBzbGlkZXJEYXRhWzFdPy5JbWFnZSxcclxuICAgICAgICBhbHQ6IFwiSW1hZ2UgMiBhbHRcIixcclxuICAgICAgfSxcclxuICAgICAge1xyXG4gICAgICAgIHNyYzogc2xpZGVyRGF0YVsyXT8uSW1hZ2UsXHJcbiAgICAgICAgYWx0OiBcIkltYWdlIDMgYWx0XCIsXHJcbiAgICAgIH0sXHJcbiAgICAgIHtcclxuICAgICAgICBzcmM6IHNsaWRlckRhdGFbM10/LkltYWdlLFxyXG4gICAgICAgIGFsdDogXCJJbWFnZSA0IGFsdFwiLFxyXG4gICAgICB9LFxyXG4gICAgXSxcclxuICB9OyAqL1xyXG5cclxuICAvLyBjaGFuZ2UgdGl0bGUgYW5kIHN1YnRpdGxlIHdpdGggdGhlIGltYWdlcywgaW4gZXZlcnkgNSBzZWNvbmRzXHJcbiAgY29uc3QgW3RpdGxlLCBzZXRUaXRsZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KHQoXCJoZXJvLnRpdGxlXCIpKTtcclxuICBjb25zdCBbc3VidGl0bGUsIHNldFN1YnRpdGxlXSA9IHVzZVN0YXRlPHN0cmluZz4odChcImhlcm8uc3VidGl0bGVcIikpO1xyXG5cclxuICBjb25zdCBbaW5kZXgsIHNldEluZGV4XSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcclxuICAgICAgc3dpdGNoIChpbmRleCkge1xyXG4gICAgICAgIGNhc2UgMDpcclxuICAgICAgICAgIHNldFRpdGxlKFxyXG4gICAgICAgICAgICBzbGlkZXJUZXh0WzBdPy5UaXRsZSA/IHNsaWRlclRleHRbMF0/LlRpdGxlIDogdChcImhlcm8udGl0bGVcIilcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBzZXRTdWJ0aXRsZShcclxuICAgICAgICAgICAgc2xpZGVyVGV4dFswXT8uQ29udGVudCA/IHNsaWRlclRleHRbMF0/LkNvbnRlbnQgOiB0KFwiaGVyby5zdWJ0aXRsZVwiKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIHNldEluZGV4KDEpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSAxOlxyXG4gICAgICAgICAgc2V0VGl0bGUoXHJcbiAgICAgICAgICAgIHNsaWRlclRleHRbMV0/LlRpdGxlID8gc2xpZGVyVGV4dFsxXT8uVGl0bGUgOiB0KFwiaGVyby50aXRsZVwiKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIHNldFN1YnRpdGxlKFxyXG4gICAgICAgICAgICBzbGlkZXJUZXh0WzFdPy5Db250ZW50XHJcbiAgICAgICAgICAgICAgPyBzbGlkZXJUZXh0WzFdPy5Db250ZW50XHJcbiAgICAgICAgICAgICAgOiB0KFwiaGVyby5zdWJ0aXRsZTJcIilcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBzZXRJbmRleCgyKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgMjpcclxuICAgICAgICAgIHNldFRpdGxlKFxyXG4gICAgICAgICAgICBzbGlkZXJUZXh0WzJdPy5UaXRsZSA/IHNsaWRlclRleHRbMl0/LlRpdGxlIDogdChcImhlcm8udGl0bGVcIilcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBzZXRTdWJ0aXRsZShcclxuICAgICAgICAgICAgc2xpZGVyVGV4dFsyXT8uQ29udGVudFxyXG4gICAgICAgICAgICAgID8gc2xpZGVyVGV4dFsyXT8uQ29udGVudFxyXG4gICAgICAgICAgICAgIDogdChcImhlcm8uc3VidGl0bGUzXCIpXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgICAgc2V0SW5kZXgoMCk7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlIDM6XHJcbiAgICAgICAgICBzZXRUaXRsZShcclxuICAgICAgICAgICAgc2xpZGVyVGV4dFszXT8uVGl0bGUgPyBzbGlkZXJUZXh0WzNdPy5UaXRsZSA6IHQoXCJoZXJvLnRpdGxlXCIpXHJcbiAgICAgICAgICApO1xyXG4gICAgICAgICAgc2V0U3VidGl0bGUoXHJcbiAgICAgICAgICAgIHNsaWRlclRleHRbM10/LkNvbnRlbnRcclxuICAgICAgICAgICAgICA/IHNsaWRlclRleHRbM10/LkNvbnRlbnRcclxuICAgICAgICAgICAgICA6IHQoXCJoZXJvLnN1YnRpdGxlNFwiKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIHNldEluZGV4KDApO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgIHNldFRpdGxlKHQoXCJoZXJvLnRpdGxlXCIpKTtcclxuICAgICAgICAgIHNldFN1YnRpdGxlKHQoXCJoZXJvLnN1YnRpdGxlXCIpKTtcclxuICAgICAgICAgIHNldEluZGV4KDEpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgIH1cclxuICAgIH0sIDQwMDApO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcclxuICB9LCBbaW5kZXgsIHQsIHNsaWRlclRleHRdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1bOTV2aF0gb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgIDxRdWl6TW9kYWwgaXNPcGVuPXtpc1F1aXpNb2RhbE9wZW59IHNldElzT3Blbj17c2V0SXNRdWl6TW9kYWxPcGVufSAvPlxyXG4gICAgICA8RnNMaWdodGJveCB0b2dnbGVyPXt0b2dnbGVyfSBzb3VyY2VzPXtbaGVyb0RhdGE/LlZpZGVvTGlua119IC8+XHJcbiAgICAgIDxGc0xpZ2h0Ym94XHJcbiAgICAgICAgdG9nZ2xlcj17YnJvY2h1cmVUb2dnbGVyfVxyXG4gICAgICAgIHNvdXJjZXM9e1toZXJvRGF0YT8uQnJvY2h1cmVMaW5rXX1cclxuICAgICAgICB0eXBlPXtcImltYWdlXCJ9XHJcbiAgICAgIC8+XHJcbiAgICAgIHshaW1hZ2VzTG9hZGluZyAmJiAoXHJcbiAgICAgICAgPEluZmluaXRlWm9vbUZhZGVyXHJcbiAgICAgICAgICBpbWFnZXM9e3Jlc3BvbnNlSW1hZ2VzfVxyXG4gICAgICAgICAgem9vbT1cIm91dFwiXHJcbiAgICAgICAgICB6b29tU2NhbGU9ezAuNzV9XHJcbiAgICAgICAgICB6b29tVGltZT17NX1cclxuICAgICAgICAgIHpvb21NYXg9ezAuMjV9XHJcbiAgICAgICAgICB6b29tVGltaW5nRnVuY3Rpb249XCJsaW5lYXJcIlxyXG4gICAgICAgICAgdHJhbnNpdGlvblRpbWU9ezF9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibGFjay81MCB3LWZ1bGwgaC1mdWxsIGFic29sdXRlIHRvcC0wIGxlZnQtMCBcIiAvPlxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBpZD1cImhvbWVcIlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJjb250YWluZXIgbWF4LXctWzE3NjBweF0geGw6cHgtWzMwcHhdIGgtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlclwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtWzI1cHhdIGl0ZW1zLWNlbnRlciBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS1bMzBweF0gbWQ6c3BhY2UteS1bNDBweF0gbGc6c3BhY2UteS1bMzBweF0geGw6c3BhY2UteS1bNDBweF0gbGc6bWF4LXctWzU5MHB4XVwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1bMjAwcHhdIG1kOm1pbi1oLVszMDBweF0gXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxoMVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YCB0ZXh0LVszNXB4XSBtZDp0ZXh0LVs0MnB4XSBsZzp0ZXh0LVs0MHB4XSB4bDp0ZXh0LVs1MHB4XSAyeGw6dGV4dC1bNTVweF0gbGVhZGluZy1bMzVweF0gbWQ6bGVhZGluZy1bNDZweF0gbGc6bGVhZGluZy1bNTVweF0geGw6bGVhZGluZy1bNjRweF0gbWItWzIwcHhdIG1kOm1iLVszMHB4XSBsZzptYi1bMjBweF0geGw6bWItWzQwcHhdIHRleHQtd2hpdGVgfVxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGEtYW9zPVwiZmFkZS11cFwiXHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS1hb3MtZGVsYXk9XCIxMDBcIlxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGEtYW9zLWR1cmF0aW9uPVwiNjAwXCJcclxuICAgICAgICAgICAgICAgICAgICBkYXRhLWFvcy1vbmNlPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICB7dGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIDwvaDE+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8cFxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHRleHQtWzE2cHhdIG1kOnRleHQtWzE4cHhdIHRleHQtc3RvbmUtMzAwYH1cclxuICAgICAgICAgICAgICAgICAgICBkYXRhLWFvcz1cImZhZGUtdXBcIlxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGEtYW9zLWRlbGF5PVwiMjAwXCJcclxuICAgICAgICAgICAgICAgICAgICBkYXRhLWFvcy1kdXJhdGlvbj1cIjYwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS1hb3Mtb25jZT1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge3N1YnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNtOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXktWzIwcHhdIHNtOnNwYWNlLXktWzBdIHNtOnNwYWNlLXgtWzMwcHhdXCJcclxuICAgICAgICAgICAgICAgICAgZGF0YS1hb3M9XCJmYWRlLXVwXCJcclxuICAgICAgICAgICAgICAgICAgZGF0YS1hb3MtZGVsYXk9XCIzMDBcIlxyXG4gICAgICAgICAgICAgICAgICBkYXRhLWFvcy1kdXJhdGlvbj1cIjYwMFwiXHJcbiAgICAgICAgICAgICAgICAgIGRhdGEtYW9zLW9uY2U9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93ICBnYXAtWzMwcHhdIFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiI2NvbnRhY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIHRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCB0ZXh0LVsxNnB4XSBtZDp0ZXh0LVsxOHB4XSB0cmFuc2l0aW9uIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPENvbnRhY3RTdXBwb3J0IHNpemU9ezQ4fSBjbGFzc05hbWU9XCJtci1bM3B4XSBtYi1bMnB4XVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7dChcImhlcm8uY29udGFjdFwiKX1cclxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtWzEwcHhdIGN1cnNvci1wb2ludGVyIGdyb3VwXCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFRvZ2dsZXIoIXRvZ2dsZXIpfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyNmZmZdIHctWzM2cHhdIGgtWzM2cHhdIGxlYWRpbmctWzM2cHhdIHBsLVs1cHhdIHJvdW5kZWQtZnVsbCB0ZXh0LWNlbnRlciB0ZXh0LVsyMnB4XSB0cmFuc2l0aW9uIGR1cmF0aW9uLTUwMCBlYXNlLWluLW91dCBncm91cC1ob3ZlcjpiZy1wcmltYXJ5LWNvbG9yIGdyb3VwLWhvdmVyOnRleHQtd2hpdGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFBsYXkgc2l6ZT17Mjh9IGNsYXNzTmFtZT1cIm1yLVszcHhdIG1iLVsycHhdXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LVsxNnB4XSBtZDp0ZXh0LVsxOHB4XSB0ZXh0LXdoaXRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0KFwiaGVyby5ob3dJdFdvcmtzXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtWzEwcHhdIGN1cnNvci1wb2ludGVyIGdyb3VwXCJcclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRCcm9jaHVyZVRvZ2dsZXIoIWJyb2NodXJlVG9nZ2xlcil9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtWzE2cHhdIG1kOnRleHQtWzE4cHhdIHRleHQtc2t5LTMwMCBob3Zlcjp0ZXh0LXNreS00MDAgbWQ6LW1sLTIgdXBwZXJjYXNlIHVuZGVybGluZSB1bmRlcmxpbmUtb2Zmc2V0LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCb29rT3BlbiBzaXplPXsyMH0gY2xhc3NOYW1lPVwibXItWzZweF0gbWItWzJweF1cIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAge3QoXCJoZXJvLmJyb2NodXJlXCIpfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNRdWl6TW9kYWxPcGVuKHRydWUpfVxyXG4gICAgICAgICAgICAgICAgICBkYXRhLWFvcz1cImZhZGUtdXBcIlxyXG4gICAgICAgICAgICAgICAgICBkYXRhLWFvcy1kZWxheT1cIjQwMFwiXHJcbiAgICAgICAgICAgICAgICAgIGRhdGEtYW9zLWR1cmF0aW9uPVwiNjAwXCJcclxuICAgICAgICAgICAgICAgICAgZGF0YS1hb3Mtb25jZT1cInRydWVcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCIgdGV4dC1zdG9uZS0yMDAgZm9udC1zZW1pYm9sZCB0ZXh0LVsxNnB4XSBtZDp0ZXh0LVsxOHB4XSB0ZXh0LWxlZnQgaXRhbGljIHVuZGVybGluZSB1bmRlcmxpbmUtb2Zmc2V0LTJcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8UXVpeiBzaXplPXsyOH0gY2xhc3NOYW1lPVwibXItWzJweF0gaW5saW5lXCIgLz5cclxuICAgICAgICAgICAgICAgICAge3QoXCJoZXJvLnRlc3RcIil9Li4uXHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L0luZmluaXRlWm9vbUZhZGVyPlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IEhlcm9CYW5uZXI7XHJcbiJdLCJuYW1lcyI6WyJDb250YWN0U3VwcG9ydCIsIkZzTGlnaHRib3giLCJJbmZpbml0ZVpvb21GYWRlciIsIkxpbmsiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVRyYW5zbGF0aW9uIiwiUXVpeiIsIlF1aXpNb2RhbCIsIkJvb2tPcGVuIiwiUGxheSIsImdldFNsaWRlciIsImdldEhlcm9CYW5uZXIiLCJnZXRIZXJvU2xpZGVyVGV4dCIsImkxOG5leHQiLCJIZXJvQmFubmVyIiwidCIsInRvZ2dsZXIiLCJzZXRUb2dnbGVyIiwiYnJvY2h1cmVUb2dnbGVyIiwic2V0QnJvY2h1cmVUb2dnbGVyIiwiaXNRdWl6TW9kYWxPcGVuIiwic2V0SXNRdWl6TW9kYWxPcGVuIiwiaGVyb0RhdGEiLCJzZXRIZXJvRGF0YSIsImxhbmd1YWdlSWQiLCJzZXRMYW5ndWFnZUlkIiwic2xpZGVyVGV4dCIsInNldFNsaWRlclRleHQiLCJpbWFnZXNMb2FkaW5nIiwic2V0SW1hZ2VzTG9hZGluZyIsInNsaWRlckRhdGEiLCJzZXRTbGlkZXJEYXRhIiwiZmV0Y2hEYXRhIiwicmVzcG9uc2UiLCJkYXRhIiwiRGF0YSIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImhhbmRsZUxhbmd1YWdlQ2hhbmdlIiwibGFuZ3VhZ2VDb2RlIiwibGFuZ3VhZ2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwib24iLCJvZmYiLCJnZXRIZXJvRGF0YSIsInJlc3BvbnNlSW1hZ2VzIiwiZGVza3RvcCIsIm1hcCIsIml0ZW0iLCJzcmMiLCJJbWFnZSIsImFsdCIsIklkIiwidG9TdHJpbmciLCJtb2JpbGUiLCJ0aXRsZSIsInNldFRpdGxlIiwic3VidGl0bGUiLCJzZXRTdWJ0aXRsZSIsImluZGV4Iiwic2V0SW5kZXgiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiVGl0bGUiLCJDb250ZW50IiwiY2xlYXJJbnRlcnZhbCIsImRpdiIsImNsYXNzTmFtZSIsImlzT3BlbiIsInNldElzT3BlbiIsInNvdXJjZXMiLCJWaWRlb0xpbmsiLCJCcm9jaHVyZUxpbmsiLCJ0eXBlIiwiaW1hZ2VzIiwiem9vbSIsInpvb21TY2FsZSIsInpvb21UaW1lIiwiem9vbU1heCIsInpvb21UaW1pbmdGdW5jdGlvbiIsInRyYW5zaXRpb25UaW1lIiwiaWQiLCJoMSIsImRhdGEtYW9zIiwiZGF0YS1hb3MtZGVsYXkiLCJkYXRhLWFvcy1kdXJhdGlvbiIsImRhdGEtYW9zLW9uY2UiLCJwIiwiaHJlZiIsInNpemUiLCJvbkNsaWNrIiwiYnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CrystalApp/HeroBanner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CrystalApp/QuizModal.tsx":
/*!*************************************************!*\
  !*** ./src/components/CrystalApp/QuizModal.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Dialog!=!@headlessui/react */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-icons/bootstrap */ \"(app-pages-browser)/./node_modules/@styled-icons/bootstrap/Whatsapp/Whatsapp.esm.js\");\n\n\n\n\nconst QuizModal = (param)=>{\n    let { isOpen, setIsOpen } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onClose: ()=>setIsOpen(false),\n        className: \"relative z-50 \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/30\",\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex w-screen items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Panel, {\n                    className: \"mx-auto max-w-sm rounded bg-stone-100 py-4 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Title, {\n                            className: \"text-xl lg:text-2xl font-bold\",\n                            children: \"Crystal Aligner ile G\\xfcl\\xfcş Testi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dialog_headlessui_react__WEBPACK_IMPORTED_MODULE_2__.Dialog.Description, {\n                            className: \"my-2 font-semibold\",\n                            children: \"Şeffaf plak tedavisi hakkında daha fazla bilgi almak ister misiniz?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Cevabınız evet ise, hemen aşağıdaki butona tıklayarak hekimlerimizle iletişime ge\\xe7in.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://wa.me/905389138948\",\n                            className: \"block mt-6  text-white bg-green-500 py-2 px-4 rounded max-w-fit hover:text-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_bootstrap__WEBPACK_IMPORTED_MODULE_3__.Whatsapp, {\n                                    className: \"inline w-5 mr-2 mb-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Hemen Whatsapp ile iletişime ge\\xe7in\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\QuizModal.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n_c = QuizModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (QuizModal);\nvar _c;\n$RefreshReg$(_c, \"QuizModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CrystalApp/QuizModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/HeroService.ts":
/*!*************************************!*\
  !*** ./src/services/HeroService.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHeroBanner: function() { return /* binding */ getHeroBanner; },\n/* harmony export */   getHeroSliderText: function() { return /* binding */ getHeroSliderText; }\n/* harmony export */ });\n/* harmony import */ var _ApiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ApiService */ \"(app-pages-browser)/./src/services/ApiService.ts\");\n\nconst getHeroBanner = async ()=>{\n    return await (0,_ApiService__WEBPACK_IMPORTED_MODULE_0__.GetWithBasic)(\"Hero\");\n};\nconst getHeroSliderText = async (languageId)=>{\n    return await (0,_ApiService__WEBPACK_IMPORTED_MODULE_0__.GetWithBasic)(\"SliderText/GetByLangugageId/\".concat(languageId));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9IZXJvU2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFFckMsTUFBTUMsZ0JBQWdCO0lBQzNCLE9BQU8sTUFBTUQseURBQVlBLENBQUU7QUFDN0IsRUFBRTtBQUVLLE1BQU1FLG9CQUFvQixPQUFPQztJQUN0QyxPQUFPLE1BQU1ILHlEQUFZQSxDQUFDLCtCQUEwQyxPQUFYRztBQUMzRCxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zZXJ2aWNlcy9IZXJvU2VydmljZS50cz80MmU1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdldFdpdGhCYXNpYyB9IGZyb20gXCIuL0FwaVNlcnZpY2VcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRIZXJvQmFubmVyID0gYXN5bmMgKCkgPT4ge1xyXG4gIHJldHVybiBhd2FpdCBHZXRXaXRoQmFzaWMoYEhlcm9gKTtcclxufTtcclxuXHJcbmV4cG9ydCBjb25zdCBnZXRIZXJvU2xpZGVyVGV4dCA9IGFzeW5jIChsYW5ndWFnZUlkOiBudW1iZXIpID0+IHtcclxuICByZXR1cm4gYXdhaXQgR2V0V2l0aEJhc2ljKGBTbGlkZXJUZXh0L0dldEJ5TGFuZ3VnYWdlSWQvJHtsYW5ndWFnZUlkfWApO1xyXG59O1xyXG4iXSwibmFtZXMiOlsiR2V0V2l0aEJhc2ljIiwiZ2V0SGVyb0Jhbm5lciIsImdldEhlcm9TbGlkZXJUZXh0IiwibGFuZ3VhZ2VJZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/HeroService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/SliderService.ts":
/*!***************************************!*\
  !*** ./src/services/SliderService.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSlider: function() { return /* binding */ getSlider; }\n/* harmony export */ });\n/* harmony import */ var _ApiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ApiService */ \"(app-pages-browser)/./src/services/ApiService.ts\");\n\nconst getSlider = async ()=>{\n    return await (0,_ApiService__WEBPACK_IMPORTED_MODULE_0__.GetWithBasic)(\"Slider\");\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zZXJ2aWNlcy9TbGlkZXJTZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJDO0FBRXBDLE1BQU1DLFlBQVk7SUFDdEIsT0FBTyxNQUFNRCx5REFBWUEsQ0FBRTtBQUM5QixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zZXJ2aWNlcy9TbGlkZXJTZXJ2aWNlLnRzP2RhNWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR2V0V2l0aEJhc2ljIH0gZnJvbSBcIi4vQXBpU2VydmljZVwiXHJcblxyXG5leHBvcnQgY29uc3QgZ2V0U2xpZGVyID0gYXN5bmMgKCkgPT4ge1xyXG4gICByZXR1cm4gYXdhaXQgR2V0V2l0aEJhc2ljKGBTbGlkZXJgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJHZXRXaXRoQmFzaWMiLCJnZXRTbGlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/SliderService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/components/description/description.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/components/description/description.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Description: function() { return /* binding */ b; },\n/* harmony export */   useDescriptions: function() { return /* binding */ M; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _hooks_use_id_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/use-id.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-id.js\");\n/* harmony import */ var _utils_render_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/render.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/render.js\");\n/* harmony import */ var _hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/use-iso-morphic-effect.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js\");\n/* harmony import */ var _hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/use-sync-refs.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-sync-refs.js\");\n/* harmony import */ var _hooks_use_event_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\nlet d=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);function f(){let r=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(d);if(r===null){let t=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(t,f),t}return r}function M(){let[r,t]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);return[r.length>0?r.join(\" \"):void 0,(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>function(e){let i=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_1__.useEvent)(s=>(t(o=>[...o,s]),()=>t(o=>{let p=o.slice(),c=p.indexOf(s);return c!==-1&&p.splice(c,1),p}))),n=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({register:i,slot:e.slot,name:e.name,props:e.props}),[i,e.slot,e.name,e.props]);return react__WEBPACK_IMPORTED_MODULE_0__.createElement(d.Provider,{value:n},e.children)},[t])]}let S=\"p\";function h(r,t){let a=(0,_hooks_use_id_js__WEBPACK_IMPORTED_MODULE_2__.useId)(),{id:e=`headlessui-description-${a}`,...i}=r,n=f(),s=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_3__.useSyncRefs)(t);(0,_hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_4__.useIsoMorphicEffect)(()=>n.register(e),[e,n.register]);let o={ref:s,...n.props,id:e};return (0,_utils_render_js__WEBPACK_IMPORTED_MODULE_5__.render)({ourProps:o,theirProps:i,slot:n.slot||{},defaultTag:S,name:n.name||\"Description\"})}let y=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_5__.forwardRefWithAs)(h),b=Object.assign(y,{});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/components/description/description.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/components/dialog/dialog.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: function() { return /* binding */ _t; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_match_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/match.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/match.js\");\n/* harmony import */ var _utils_render_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/render.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/render.js\");\n/* harmony import */ var _hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../hooks/use-sync-refs.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-sync-refs.js\");\n/* harmony import */ var _keyboard_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../keyboard.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/keyboard.js\");\n/* harmony import */ var _utils_bugs_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../../utils/bugs.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/bugs.js\");\n/* harmony import */ var _hooks_use_id_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/use-id.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-id.js\");\n/* harmony import */ var _components_focus_trap_focus_trap_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../components/focus-trap/focus-trap.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.js\");\n/* harmony import */ var _components_portal_portal_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/portal/portal.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/portal/portal.js\");\n/* harmony import */ var _internal_portal_force_root_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../internal/portal-force-root.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/portal-force-root.js\");\n/* harmony import */ var _description_description_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../description/description.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/components/description/description.js\");\n/* harmony import */ var _internal_open_closed_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../internal/open-closed.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/open-closed.js\");\n/* harmony import */ var _hooks_use_server_handoff_complete_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../hooks/use-server-handoff-complete.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js\");\n/* harmony import */ var _internal_stack_context_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../internal/stack-context.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/stack-context.js\");\n/* harmony import */ var _hooks_use_outside_click_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../hooks/use-outside-click.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-outside-click.js\");\n/* harmony import */ var _hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../hooks/use-owner.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-owner.js\");\n/* harmony import */ var _hooks_use_event_listener_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../hooks/use-event-listener.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event-listener.js\");\n/* harmony import */ var _hooks_use_event_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../hooks/use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\n/* harmony import */ var _hooks_document_overflow_use_document_overflow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/document-overflow/use-document-overflow.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js\");\n/* harmony import */ var _hooks_use_inert_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/use-inert.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-inert.js\");\n/* harmony import */ var _hooks_use_root_containers_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../hooks/use-root-containers.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-root-containers.js\");\nvar _e=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))(_e||{}),Ie=(e=>(e[e.SetTitleId=0]=\"SetTitleId\",e))(Ie||{});let Me={[0](t,e){return t.titleId===e.id?t:{...t,titleId:e.id}}},I=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);I.displayName=\"DialogContext\";function b(t){let e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(I);if(e===null){let o=new Error(`<${t} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,b),o}return e}function we(t,e,o=()=>[document.body]){(0,_hooks_document_overflow_use_document_overflow_js__WEBPACK_IMPORTED_MODULE_1__.useDocumentOverflowLockedEffect)(t,e,i=>{var n;return{containers:[...(n=i.containers)!=null?n:[],o]}})}function Be(t,e){return (0,_utils_match_js__WEBPACK_IMPORTED_MODULE_2__.match)(e.type,Me,t,e)}let He=\"div\",Ge=_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.Features.RenderStrategy|_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.Features.Static;function Ne(t,e){var X;let o=(0,_hooks_use_id_js__WEBPACK_IMPORTED_MODULE_4__.useId)(),{id:i=`headlessui-dialog-${o}`,open:n,onClose:l,initialFocus:s,__demoMode:g=!1,...T}=t,[m,h]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0),a=(0,_internal_open_closed_js__WEBPACK_IMPORTED_MODULE_5__.useOpenClosed)();n===void 0&&a!==null&&(n=(a&_internal_open_closed_js__WEBPACK_IMPORTED_MODULE_5__.State.Open)===_internal_open_closed_js__WEBPACK_IMPORTED_MODULE_5__.State.Open);let D=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),Q=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_6__.useSyncRefs)(D,e),f=(0,_hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_7__.useOwnerDocument)(D),N=t.hasOwnProperty(\"open\")||a!==null,U=t.hasOwnProperty(\"onClose\");if(!N&&!U)throw new Error(\"You have to provide an `open` and an `onClose` prop to the `Dialog` component.\");if(!N)throw new Error(\"You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.\");if(!U)throw new Error(\"You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.\");if(typeof n!=\"boolean\")throw new Error(`You provided an \\`open\\` prop to the \\`Dialog\\`, but the value is not a boolean. Received: ${n}`);if(typeof l!=\"function\")throw new Error(`You provided an \\`onClose\\` prop to the \\`Dialog\\`, but the value is not a function. Received: ${l}`);let p=n?0:1,[S,Z]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(Be,{titleId:null,descriptionId:null,panelRef:(0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)()}),P=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_8__.useEvent)(()=>l(!1)),W=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_8__.useEvent)(r=>Z({type:0,id:r})),L=(0,_hooks_use_server_handoff_complete_js__WEBPACK_IMPORTED_MODULE_9__.useServerHandoffComplete)()?g?!1:p===0:!1,F=m>1,Y=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(I)!==null,[ee,te]=(0,_components_portal_portal_js__WEBPACK_IMPORTED_MODULE_10__.useNestedPortals)(),{resolveContainers:M,mainTreeNodeRef:k,MainTreeNode:oe}=(0,_hooks_use_root_containers_js__WEBPACK_IMPORTED_MODULE_11__.useRootContainers)({portals:ee,defaultContainers:[(X=S.panelRef.current)!=null?X:D.current]}),re=F?\"parent\":\"leaf\",$=a!==null?(a&_internal_open_closed_js__WEBPACK_IMPORTED_MODULE_5__.State.Closing)===_internal_open_closed_js__WEBPACK_IMPORTED_MODULE_5__.State.Closing:!1,ne=(()=>Y||$?!1:L)(),le=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{var r,c;return(c=Array.from((r=f==null?void 0:f.querySelectorAll(\"body > *\"))!=null?r:[]).find(d=>d.id===\"headlessui-portal-root\"?!1:d.contains(k.current)&&d instanceof HTMLElement))!=null?c:null},[k]);(0,_hooks_use_inert_js__WEBPACK_IMPORTED_MODULE_12__.useInert)(le,ne);let ae=(()=>F?!0:L)(),ie=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{var r,c;return(c=Array.from((r=f==null?void 0:f.querySelectorAll(\"[data-headlessui-portal]\"))!=null?r:[]).find(d=>d.contains(k.current)&&d instanceof HTMLElement))!=null?c:null},[k]);(0,_hooks_use_inert_js__WEBPACK_IMPORTED_MODULE_12__.useInert)(ie,ae);let se=(()=>!(!L||F))();(0,_hooks_use_outside_click_js__WEBPACK_IMPORTED_MODULE_13__.useOutsideClick)(M,P,se);let pe=(()=>!(F||p!==0))();(0,_hooks_use_event_listener_js__WEBPACK_IMPORTED_MODULE_14__.useEventListener)(f==null?void 0:f.defaultView,\"keydown\",r=>{pe&&(r.defaultPrevented||r.key===_keyboard_js__WEBPACK_IMPORTED_MODULE_15__.Keys.Escape&&(r.preventDefault(),r.stopPropagation(),P()))});let de=(()=>!($||p!==0||Y))();we(f,de,M),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{if(p!==0||!D.current)return;let r=new ResizeObserver(c=>{for(let d of c){let x=d.target.getBoundingClientRect();x.x===0&&x.y===0&&x.width===0&&x.height===0&&P()}});return r.observe(D.current),()=>r.disconnect()},[p,D,P]);let[ue,fe]=(0,_description_description_js__WEBPACK_IMPORTED_MODULE_16__.useDescriptions)(),ge=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>[{dialogState:p,close:P,setTitleId:W},S],[p,S,P,W]),J=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({open:p===0}),[p]),Te={ref:Q,id:i,role:\"dialog\",\"aria-modal\":p===0?!0:void 0,\"aria-labelledby\":S.titleId,\"aria-describedby\":ue};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_stack_context_js__WEBPACK_IMPORTED_MODULE_17__.StackProvider,{type:\"Dialog\",enabled:p===0,element:D,onUpdate:(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_8__.useEvent)((r,c)=>{c===\"Dialog\"&&(0,_utils_match_js__WEBPACK_IMPORTED_MODULE_2__.match)(r,{[_internal_stack_context_js__WEBPACK_IMPORTED_MODULE_17__.StackMessage.Add]:()=>h(d=>d+1),[_internal_stack_context_js__WEBPACK_IMPORTED_MODULE_17__.StackMessage.Remove]:()=>h(d=>d-1)})})},react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_portal_force_root_js__WEBPACK_IMPORTED_MODULE_18__.ForcePortalRoot,{force:!0},react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_portal_portal_js__WEBPACK_IMPORTED_MODULE_10__.Portal,null,react__WEBPACK_IMPORTED_MODULE_0__.createElement(I.Provider,{value:ge},react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_portal_portal_js__WEBPACK_IMPORTED_MODULE_10__.Portal.Group,{target:D},react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_portal_force_root_js__WEBPACK_IMPORTED_MODULE_18__.ForcePortalRoot,{force:!1},react__WEBPACK_IMPORTED_MODULE_0__.createElement(fe,{slot:J,name:\"Dialog.Description\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_focus_trap_focus_trap_js__WEBPACK_IMPORTED_MODULE_19__.FocusTrap,{initialFocus:s,containers:M,features:L?(0,_utils_match_js__WEBPACK_IMPORTED_MODULE_2__.match)(re,{parent:_components_focus_trap_focus_trap_js__WEBPACK_IMPORTED_MODULE_19__.FocusTrap.features.RestoreFocus,leaf:_components_focus_trap_focus_trap_js__WEBPACK_IMPORTED_MODULE_19__.FocusTrap.features.All&~_components_focus_trap_focus_trap_js__WEBPACK_IMPORTED_MODULE_19__.FocusTrap.features.FocusLock}):_components_focus_trap_focus_trap_js__WEBPACK_IMPORTED_MODULE_19__.FocusTrap.features.None},react__WEBPACK_IMPORTED_MODULE_0__.createElement(te,null,(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.render)({ourProps:Te,theirProps:T,slot:J,defaultTag:He,features:Ge,visible:p===0,name:\"Dialog\"}))))))))),react__WEBPACK_IMPORTED_MODULE_0__.createElement(oe,null))}let Ue=\"div\";function We(t,e){let o=(0,_hooks_use_id_js__WEBPACK_IMPORTED_MODULE_4__.useId)(),{id:i=`headlessui-dialog-overlay-${o}`,...n}=t,[{dialogState:l,close:s}]=b(\"Dialog.Overlay\"),g=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_6__.useSyncRefs)(e),T=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_8__.useEvent)(a=>{if(a.target===a.currentTarget){if((0,_utils_bugs_js__WEBPACK_IMPORTED_MODULE_20__.isDisabledReactIssue7711)(a.currentTarget))return a.preventDefault();a.preventDefault(),a.stopPropagation(),s()}}),m=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({open:l===0}),[l]);return (0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.render)({ourProps:{ref:g,id:i,\"aria-hidden\":!0,onClick:T},theirProps:n,slot:m,defaultTag:Ue,name:\"Dialog.Overlay\"})}let Ye=\"div\";function $e(t,e){let o=(0,_hooks_use_id_js__WEBPACK_IMPORTED_MODULE_4__.useId)(),{id:i=`headlessui-dialog-backdrop-${o}`,...n}=t,[{dialogState:l},s]=b(\"Dialog.Backdrop\"),g=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_6__.useSyncRefs)(e);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{if(s.panelRef.current===null)throw new Error(\"A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.\")},[s.panelRef]);let T=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({open:l===0}),[l]);return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_portal_force_root_js__WEBPACK_IMPORTED_MODULE_18__.ForcePortalRoot,{force:!0},react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_portal_portal_js__WEBPACK_IMPORTED_MODULE_10__.Portal,null,(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.render)({ourProps:{ref:g,id:i,\"aria-hidden\":!0},theirProps:n,slot:T,defaultTag:Ye,name:\"Dialog.Backdrop\"})))}let Je=\"div\";function Xe(t,e){let o=(0,_hooks_use_id_js__WEBPACK_IMPORTED_MODULE_4__.useId)(),{id:i=`headlessui-dialog-panel-${o}`,...n}=t,[{dialogState:l},s]=b(\"Dialog.Panel\"),g=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_6__.useSyncRefs)(e,s.panelRef),T=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({open:l===0}),[l]),m=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_8__.useEvent)(a=>{a.stopPropagation()});return (0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.render)({ourProps:{ref:g,id:i,onClick:m},theirProps:n,slot:T,defaultTag:Je,name:\"Dialog.Panel\"})}let je=\"h2\";function Ke(t,e){let o=(0,_hooks_use_id_js__WEBPACK_IMPORTED_MODULE_4__.useId)(),{id:i=`headlessui-dialog-title-${o}`,...n}=t,[{dialogState:l,setTitleId:s}]=b(\"Dialog.Title\"),g=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_6__.useSyncRefs)(e);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>(s(i),()=>s(null)),[i,s]);let T=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({open:l===0}),[l]);return (0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.render)({ourProps:{ref:g,id:i},theirProps:n,slot:T,defaultTag:je,name:\"Dialog.Title\"})}let Ve=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.forwardRefWithAs)(Ne),qe=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.forwardRefWithAs)($e),ze=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.forwardRefWithAs)(Xe),Qe=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.forwardRefWithAs)(We),Ze=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_3__.forwardRefWithAs)(Ke),_t=Object.assign(Ve,{Backdrop:qe,Panel:ze,Overlay:Qe,Title:Ze,Description:_description_description_js__WEBPACK_IMPORTED_MODULE_16__.Description});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/components/dialog/dialog.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusTrap: function() { return /* binding */ ge; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_render_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utils/render.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/render.js\");\n/* harmony import */ var _hooks_use_server_handoff_complete_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/use-server-handoff-complete.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js\");\n/* harmony import */ var _hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../hooks/use-sync-refs.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-sync-refs.js\");\n/* harmony import */ var _internal_hidden_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../internal/hidden.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/hidden.js\");\n/* harmony import */ var _utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../utils/focus-management.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/focus-management.js\");\n/* harmony import */ var _utils_match_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/match.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/match.js\");\n/* harmony import */ var _hooks_use_event_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\n/* harmony import */ var _hooks_use_tab_direction_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks/use-tab-direction.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-tab-direction.js\");\n/* harmony import */ var _hooks_use_is_mounted_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../hooks/use-is-mounted.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-is-mounted.js\");\n/* harmony import */ var _hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/use-owner.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-owner.js\");\n/* harmony import */ var _hooks_use_event_listener_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../hooks/use-event-listener.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event-listener.js\");\n/* harmony import */ var _utils_micro_task_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../utils/micro-task.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/micro-task.js\");\n/* harmony import */ var _hooks_use_watch_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../hooks/use-watch.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-watch.js\");\n/* harmony import */ var _hooks_use_disposables_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../hooks/use-disposables.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-disposables.js\");\n/* harmony import */ var _utils_document_ready_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils/document-ready.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/document-ready.js\");\n/* harmony import */ var _hooks_use_on_unmount_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../hooks/use-on-unmount.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-on-unmount.js\");\nfunction P(t){if(!t)return new Set;if(typeof t==\"function\")return new Set(t());let r=new Set;for(let e of t.current)e.current instanceof HTMLElement&&r.add(e.current);return r}let J=\"div\";var h=(n=>(n[n.None=1]=\"None\",n[n.InitialFocus=2]=\"InitialFocus\",n[n.TabLock=4]=\"TabLock\",n[n.FocusLock=8]=\"FocusLock\",n[n.RestoreFocus=16]=\"RestoreFocus\",n[n.All=30]=\"All\",n))(h||{});function X(t,r){let e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),o=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_1__.useSyncRefs)(e,r),{initialFocus:u,containers:i,features:n=30,...l}=t;(0,_hooks_use_server_handoff_complete_js__WEBPACK_IMPORTED_MODULE_2__.useServerHandoffComplete)()||(n=1);let m=(0,_hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_3__.useOwnerDocument)(e);Y({ownerDocument:m},Boolean(n&16));let c=Z({ownerDocument:m,container:e,initialFocus:u},Boolean(n&2));$({ownerDocument:m,container:e,containers:i,previousActiveElement:c},Boolean(n&8));let v=(0,_hooks_use_tab_direction_js__WEBPACK_IMPORTED_MODULE_4__.useTabDirection)(),y=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_5__.useEvent)(s=>{let T=e.current;if(!T)return;(B=>B())(()=>{(0,_utils_match_js__WEBPACK_IMPORTED_MODULE_6__.match)(v.current,{[_hooks_use_tab_direction_js__WEBPACK_IMPORTED_MODULE_4__.Direction.Forwards]:()=>{(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusIn)(T,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.Focus.First,{skipElements:[s.relatedTarget]})},[_hooks_use_tab_direction_js__WEBPACK_IMPORTED_MODULE_4__.Direction.Backwards]:()=>{(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusIn)(T,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.Focus.Last,{skipElements:[s.relatedTarget]})}})})}),_=(0,_hooks_use_disposables_js__WEBPACK_IMPORTED_MODULE_8__.useDisposables)(),b=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1),j={ref:o,onKeyDown(s){s.key==\"Tab\"&&(b.current=!0,_.requestAnimationFrame(()=>{b.current=!1}))},onBlur(s){let T=P(i);e.current instanceof HTMLElement&&T.add(e.current);let d=s.relatedTarget;d instanceof HTMLElement&&d.dataset.headlessuiFocusGuard!==\"true\"&&(S(T,d)||(b.current?(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusIn)(e.current,(0,_utils_match_js__WEBPACK_IMPORTED_MODULE_6__.match)(v.current,{[_hooks_use_tab_direction_js__WEBPACK_IMPORTED_MODULE_4__.Direction.Forwards]:()=>_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.Focus.Next,[_hooks_use_tab_direction_js__WEBPACK_IMPORTED_MODULE_4__.Direction.Backwards]:()=>_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.Focus.Previous})|_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.Focus.WrapAround,{relativeTo:s.target}):s.target instanceof HTMLElement&&(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(s.target)))}};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment,null,Boolean(n&4)&&react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_hidden_js__WEBPACK_IMPORTED_MODULE_9__.Hidden,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:y,features:_internal_hidden_js__WEBPACK_IMPORTED_MODULE_9__.Features.Focusable}),(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_10__.render)({ourProps:j,theirProps:l,defaultTag:J,name:\"FocusTrap\"}),Boolean(n&4)&&react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_hidden_js__WEBPACK_IMPORTED_MODULE_9__.Hidden,{as:\"button\",type:\"button\",\"data-headlessui-focus-guard\":!0,onFocus:y,features:_internal_hidden_js__WEBPACK_IMPORTED_MODULE_9__.Features.Focusable}))}let z=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_10__.forwardRefWithAs)(X),ge=Object.assign(z,{features:h}),a=[];(0,_utils_document_ready_js__WEBPACK_IMPORTED_MODULE_11__.onDocumentReady)(()=>{function t(r){r.target instanceof HTMLElement&&r.target!==document.body&&a[0]!==r.target&&(a.unshift(r.target),a=a.filter(e=>e!=null&&e.isConnected),a.splice(10))}window.addEventListener(\"click\",t,{capture:!0}),window.addEventListener(\"mousedown\",t,{capture:!0}),window.addEventListener(\"focus\",t,{capture:!0}),document.body.addEventListener(\"click\",t,{capture:!0}),document.body.addEventListener(\"mousedown\",t,{capture:!0}),document.body.addEventListener(\"focus\",t,{capture:!0})});function Q(t=!0){let r=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(a.slice());return (0,_hooks_use_watch_js__WEBPACK_IMPORTED_MODULE_12__.useWatch)(([e],[o])=>{o===!0&&e===!1&&(0,_utils_micro_task_js__WEBPACK_IMPORTED_MODULE_13__.microTask)(()=>{r.current.splice(0)}),o===!1&&e===!0&&(r.current=a.slice())},[t,a,r]),(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_5__.useEvent)(()=>{var e;return(e=r.current.find(o=>o!=null&&o.isConnected))!=null?e:null})}function Y({ownerDocument:t},r){let e=Q(r);(0,_hooks_use_watch_js__WEBPACK_IMPORTED_MODULE_12__.useWatch)(()=>{r||(t==null?void 0:t.activeElement)===(t==null?void 0:t.body)&&(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(e())},[r]),(0,_hooks_use_on_unmount_js__WEBPACK_IMPORTED_MODULE_14__.useOnUnmount)(()=>{r&&(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(e())})}function Z({ownerDocument:t,container:r,initialFocus:e},o){let u=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),i=(0,_hooks_use_is_mounted_js__WEBPACK_IMPORTED_MODULE_15__.useIsMounted)();return (0,_hooks_use_watch_js__WEBPACK_IMPORTED_MODULE_12__.useWatch)(()=>{if(!o)return;let n=r.current;n&&(0,_utils_micro_task_js__WEBPACK_IMPORTED_MODULE_13__.microTask)(()=>{if(!i.current)return;let l=t==null?void 0:t.activeElement;if(e!=null&&e.current){if((e==null?void 0:e.current)===l){u.current=l;return}}else if(n.contains(l)){u.current=l;return}e!=null&&e.current?(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(e.current):(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusIn)(n,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.Focus.First)===_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.FocusResult.Error&&console.warn(\"There are no focusable elements inside the <FocusTrap />\"),u.current=t==null?void 0:t.activeElement})},[o]),u}function $({ownerDocument:t,container:r,containers:e,previousActiveElement:o},u){let i=(0,_hooks_use_is_mounted_js__WEBPACK_IMPORTED_MODULE_15__.useIsMounted)();(0,_hooks_use_event_listener_js__WEBPACK_IMPORTED_MODULE_16__.useEventListener)(t==null?void 0:t.defaultView,\"focus\",n=>{if(!u||!i.current)return;let l=P(e);r.current instanceof HTMLElement&&l.add(r.current);let m=o.current;if(!m)return;let c=n.target;c&&c instanceof HTMLElement?S(l,c)?(o.current=c,(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(c)):(n.preventDefault(),n.stopPropagation(),(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(m)):(0,_utils_focus_management_js__WEBPACK_IMPORTED_MODULE_7__.focusElement)(o.current)},!0)}function S(t,r){for(let e of t)if(e.contains(r))return!0;return!1}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2NvbXBvbmVudHMvZm9jdXMtdHJhcC9mb2N1cy10cmFwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3akMsY0FBYyxxQkFBcUIsNENBQTRDLGNBQWMsMEVBQTBFLFNBQVMsWUFBWSxzTEFBc0wsRUFBRSxnQkFBZ0IsTUFBTSw2Q0FBQyxTQUFTLG9FQUFDLE9BQU8sK0NBQStDLEdBQUcsK0ZBQUMsVUFBVSxNQUFNLHFFQUFDLElBQUksR0FBRyxnQkFBZ0IsZ0JBQWdCLFNBQVMsMkNBQTJDLGVBQWUsR0FBRyxpRUFBaUUsZUFBZSxNQUFNLDRFQUFDLEtBQUssNkRBQUMsS0FBSyxnQkFBZ0IsYUFBYSxjQUFjLHNEQUFDLFlBQVksQ0FBQyxrRUFBQyxnQkFBZ0IsbUVBQUMsR0FBRyw2REFBQyxRQUFRLCtCQUErQixFQUFFLEVBQUUsa0VBQUMsaUJBQWlCLG1FQUFDLEdBQUcsNkRBQUMsT0FBTywrQkFBK0IsR0FBRyxFQUFFLEVBQUUsSUFBSSx5RUFBQyxLQUFLLDZDQUFDLFFBQVEsbUJBQW1CLHlEQUF5RCxhQUFhLEdBQUcsV0FBVyxXQUFXLG1EQUFtRCxzQkFBc0IsdUZBQXVGLG1FQUFDLFdBQVcsc0RBQUMsWUFBWSxDQUFDLGtFQUFDLGVBQWUsNkRBQUMsT0FBTyxrRUFBQyxnQkFBZ0IsNkRBQUMsVUFBVSxFQUFFLDZEQUFDLGFBQWEsb0JBQW9CLG1DQUFtQyx3RUFBQyxlQUFlLE9BQU8sZ0RBQWUsQ0FBQywyQ0FBVSxvQkFBb0IsZ0RBQWUsQ0FBQyx1REFBQyxFQUFFLDhFQUE4RSx5REFBQyxXQUFXLEVBQUUseURBQUMsRUFBRSxzREFBc0QsZ0JBQWdCLGdEQUFlLENBQUMsdURBQUMsRUFBRSw4RUFBOEUseURBQUMsV0FBVyxHQUFHLE1BQU0sbUVBQUMsd0JBQXdCLFdBQVcsT0FBTywwRUFBQyxNQUFNLGNBQWMscUpBQXFKLG1DQUFtQyxXQUFXLHlDQUF5QyxXQUFXLHFDQUFxQyxXQUFXLDRDQUE0QyxXQUFXLGdEQUFnRCxXQUFXLDRDQUE0QyxXQUFXLEVBQUUsRUFBRSxpQkFBaUIsTUFBTSw2Q0FBQyxZQUFZLE9BQU8sOERBQUMsYUFBYSxnQkFBZ0IsZ0VBQUMsTUFBTSxvQkFBb0Isd0NBQXdDLFVBQVUsNkRBQUMsTUFBTSxNQUFNLGlFQUFpRSxFQUFFLFlBQVksZ0JBQWdCLElBQUksV0FBVyw4REFBQyxNQUFNLCtEQUErRCx3RUFBQyxNQUFNLE1BQU0sdUVBQUMsTUFBTSxHQUFHLHdFQUFDLE1BQU0sRUFBRSxZQUFZLDJDQUEyQyxJQUFJLE1BQU0sNkNBQUMsU0FBUyx1RUFBQyxHQUFHLE9BQU8sOERBQUMsTUFBTSxhQUFhLGdCQUFnQixHQUFHLGdFQUFDLE1BQU0scUJBQXFCLHFDQUFxQyx1QkFBdUIsbUNBQW1DLFlBQVksUUFBUSx1QkFBdUIsWUFBWSxPQUFPLG1CQUFtQix3RUFBQyxZQUFZLG1FQUFDLEdBQUcsNkRBQUMsVUFBVSxtRUFBQywwSEFBMEgsRUFBRSxRQUFRLFlBQVksaUVBQWlFLElBQUksTUFBTSx1RUFBQyxHQUFHLCtFQUFDLDBDQUEwQyx5QkFBeUIsV0FBVyxtREFBbUQsZ0JBQWdCLGFBQWEsZUFBZSxnREFBZ0Qsd0VBQUMsNkNBQTZDLHdFQUFDLEtBQUssd0VBQUMsWUFBWSxLQUFLLGdCQUFnQix5Q0FBeUMsU0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZWFkbGVzc3VpL3JlYWN0L2Rpc3QvY29tcG9uZW50cy9mb2N1cy10cmFwL2ZvY3VzLXRyYXAuanM/NjUzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRSx7dXNlUmVmIGFzIEx9ZnJvbVwicmVhY3RcIjtpbXBvcnR7Zm9yd2FyZFJlZldpdGhBcyBhcyBVLHJlbmRlciBhcyBOfWZyb20nLi4vLi4vdXRpbHMvcmVuZGVyLmpzJztpbXBvcnR7dXNlU2VydmVySGFuZG9mZkNvbXBsZXRlIGFzIEl9ZnJvbScuLi8uLi9ob29rcy91c2Utc2VydmVyLWhhbmRvZmYtY29tcGxldGUuanMnO2ltcG9ydHt1c2VTeW5jUmVmcyBhcyB4fWZyb20nLi4vLi4vaG9va3MvdXNlLXN5bmMtcmVmcy5qcyc7aW1wb3J0e0ZlYXR1cmVzIGFzIFIsSGlkZGVuIGFzIGd9ZnJvbScuLi8uLi9pbnRlcm5hbC9oaWRkZW4uanMnO2ltcG9ydHtmb2N1c0VsZW1lbnQgYXMgZixmb2N1c0luIGFzIE0sRm9jdXMgYXMgcCxGb2N1c1Jlc3VsdCBhcyB3fWZyb20nLi4vLi4vdXRpbHMvZm9jdXMtbWFuYWdlbWVudC5qcyc7aW1wb3J0e21hdGNoIGFzIGt9ZnJvbScuLi8uLi91dGlscy9tYXRjaC5qcyc7aW1wb3J0e3VzZUV2ZW50IGFzIEF9ZnJvbScuLi8uLi9ob29rcy91c2UtZXZlbnQuanMnO2ltcG9ydHt1c2VUYWJEaXJlY3Rpb24gYXMgRyxEaXJlY3Rpb24gYXMgSH1mcm9tJy4uLy4uL2hvb2tzL3VzZS10YWItZGlyZWN0aW9uLmpzJztpbXBvcnR7dXNlSXNNb3VudGVkIGFzIEN9ZnJvbScuLi8uLi9ob29rcy91c2UtaXMtbW91bnRlZC5qcyc7aW1wb3J0e3VzZU93bmVyRG9jdW1lbnQgYXMgS31mcm9tJy4uLy4uL2hvb2tzL3VzZS1vd25lci5qcyc7aW1wb3J0e3VzZUV2ZW50TGlzdGVuZXIgYXMgV31mcm9tJy4uLy4uL2hvb2tzL3VzZS1ldmVudC1saXN0ZW5lci5qcyc7aW1wb3J0e21pY3JvVGFzayBhcyBPfWZyb20nLi4vLi4vdXRpbHMvbWljcm8tdGFzay5qcyc7aW1wb3J0e3VzZVdhdGNoIGFzIEZ9ZnJvbScuLi8uLi9ob29rcy91c2Utd2F0Y2guanMnO2ltcG9ydHt1c2VEaXNwb3NhYmxlcyBhcyBWfWZyb20nLi4vLi4vaG9va3MvdXNlLWRpc3Bvc2FibGVzLmpzJztpbXBvcnR7b25Eb2N1bWVudFJlYWR5IGFzIHF9ZnJvbScuLi8uLi91dGlscy9kb2N1bWVudC1yZWFkeS5qcyc7aW1wb3J0e3VzZU9uVW5tb3VudCBhcyBEfWZyb20nLi4vLi4vaG9va3MvdXNlLW9uLXVubW91bnQuanMnO2Z1bmN0aW9uIFAodCl7aWYoIXQpcmV0dXJuIG5ldyBTZXQ7aWYodHlwZW9mIHQ9PVwiZnVuY3Rpb25cIilyZXR1cm4gbmV3IFNldCh0KCkpO2xldCByPW5ldyBTZXQ7Zm9yKGxldCBlIG9mIHQuY3VycmVudCllLmN1cnJlbnQgaW5zdGFuY2VvZiBIVE1MRWxlbWVudCYmci5hZGQoZS5jdXJyZW50KTtyZXR1cm4gcn1sZXQgSj1cImRpdlwiO3ZhciBoPShuPT4obltuLk5vbmU9MV09XCJOb25lXCIsbltuLkluaXRpYWxGb2N1cz0yXT1cIkluaXRpYWxGb2N1c1wiLG5bbi5UYWJMb2NrPTRdPVwiVGFiTG9ja1wiLG5bbi5Gb2N1c0xvY2s9OF09XCJGb2N1c0xvY2tcIixuW24uUmVzdG9yZUZvY3VzPTE2XT1cIlJlc3RvcmVGb2N1c1wiLG5bbi5BbGw9MzBdPVwiQWxsXCIsbikpKGh8fHt9KTtmdW5jdGlvbiBYKHQscil7bGV0IGU9TChudWxsKSxvPXgoZSxyKSx7aW5pdGlhbEZvY3VzOnUsY29udGFpbmVyczppLGZlYXR1cmVzOm49MzAsLi4ubH09dDtJKCl8fChuPTEpO2xldCBtPUsoZSk7WSh7b3duZXJEb2N1bWVudDptfSxCb29sZWFuKG4mMTYpKTtsZXQgYz1aKHtvd25lckRvY3VtZW50Om0sY29udGFpbmVyOmUsaW5pdGlhbEZvY3VzOnV9LEJvb2xlYW4obiYyKSk7JCh7b3duZXJEb2N1bWVudDptLGNvbnRhaW5lcjplLGNvbnRhaW5lcnM6aSxwcmV2aW91c0FjdGl2ZUVsZW1lbnQ6Y30sQm9vbGVhbihuJjgpKTtsZXQgdj1HKCkseT1BKHM9PntsZXQgVD1lLmN1cnJlbnQ7aWYoIVQpcmV0dXJuOyhCPT5CKCkpKCgpPT57ayh2LmN1cnJlbnQse1tILkZvcndhcmRzXTooKT0+e00oVCxwLkZpcnN0LHtza2lwRWxlbWVudHM6W3MucmVsYXRlZFRhcmdldF19KX0sW0guQmFja3dhcmRzXTooKT0+e00oVCxwLkxhc3Qse3NraXBFbGVtZW50czpbcy5yZWxhdGVkVGFyZ2V0XX0pfX0pfSl9KSxfPVYoKSxiPUwoITEpLGo9e3JlZjpvLG9uS2V5RG93bihzKXtzLmtleT09XCJUYWJcIiYmKGIuY3VycmVudD0hMCxfLnJlcXVlc3RBbmltYXRpb25GcmFtZSgoKT0+e2IuY3VycmVudD0hMX0pKX0sb25CbHVyKHMpe2xldCBUPVAoaSk7ZS5jdXJyZW50IGluc3RhbmNlb2YgSFRNTEVsZW1lbnQmJlQuYWRkKGUuY3VycmVudCk7bGV0IGQ9cy5yZWxhdGVkVGFyZ2V0O2QgaW5zdGFuY2VvZiBIVE1MRWxlbWVudCYmZC5kYXRhc2V0LmhlYWRsZXNzdWlGb2N1c0d1YXJkIT09XCJ0cnVlXCImJihTKFQsZCl8fChiLmN1cnJlbnQ/TShlLmN1cnJlbnQsayh2LmN1cnJlbnQse1tILkZvcndhcmRzXTooKT0+cC5OZXh0LFtILkJhY2t3YXJkc106KCk9PnAuUHJldmlvdXN9KXxwLldyYXBBcm91bmQse3JlbGF0aXZlVG86cy50YXJnZXR9KTpzLnRhcmdldCBpbnN0YW5jZW9mIEhUTUxFbGVtZW50JiZmKHMudGFyZ2V0KSkpfX07cmV0dXJuIEUuY3JlYXRlRWxlbWVudChFLkZyYWdtZW50LG51bGwsQm9vbGVhbihuJjQpJiZFLmNyZWF0ZUVsZW1lbnQoZyx7YXM6XCJidXR0b25cIix0eXBlOlwiYnV0dG9uXCIsXCJkYXRhLWhlYWRsZXNzdWktZm9jdXMtZ3VhcmRcIjohMCxvbkZvY3VzOnksZmVhdHVyZXM6Ui5Gb2N1c2FibGV9KSxOKHtvdXJQcm9wczpqLHRoZWlyUHJvcHM6bCxkZWZhdWx0VGFnOkosbmFtZTpcIkZvY3VzVHJhcFwifSksQm9vbGVhbihuJjQpJiZFLmNyZWF0ZUVsZW1lbnQoZyx7YXM6XCJidXR0b25cIix0eXBlOlwiYnV0dG9uXCIsXCJkYXRhLWhlYWRsZXNzdWktZm9jdXMtZ3VhcmRcIjohMCxvbkZvY3VzOnksZmVhdHVyZXM6Ui5Gb2N1c2FibGV9KSl9bGV0IHo9VShYKSxnZT1PYmplY3QuYXNzaWduKHose2ZlYXR1cmVzOmh9KSxhPVtdO3EoKCk9PntmdW5jdGlvbiB0KHIpe3IudGFyZ2V0IGluc3RhbmNlb2YgSFRNTEVsZW1lbnQmJnIudGFyZ2V0IT09ZG9jdW1lbnQuYm9keSYmYVswXSE9PXIudGFyZ2V0JiYoYS51bnNoaWZ0KHIudGFyZ2V0KSxhPWEuZmlsdGVyKGU9PmUhPW51bGwmJmUuaXNDb25uZWN0ZWQpLGEuc3BsaWNlKDEwKSl9d2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJjbGlja1wiLHQse2NhcHR1cmU6ITB9KSx3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLHQse2NhcHR1cmU6ITB9KSx3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcImZvY3VzXCIsdCx7Y2FwdHVyZTohMH0pLGRvY3VtZW50LmJvZHkuYWRkRXZlbnRMaXN0ZW5lcihcImNsaWNrXCIsdCx7Y2FwdHVyZTohMH0pLGRvY3VtZW50LmJvZHkuYWRkRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLHQse2NhcHR1cmU6ITB9KSxkb2N1bWVudC5ib2R5LmFkZEV2ZW50TGlzdGVuZXIoXCJmb2N1c1wiLHQse2NhcHR1cmU6ITB9KX0pO2Z1bmN0aW9uIFEodD0hMCl7bGV0IHI9TChhLnNsaWNlKCkpO3JldHVybiBGKChbZV0sW29dKT0+e289PT0hMCYmZT09PSExJiZPKCgpPT57ci5jdXJyZW50LnNwbGljZSgwKX0pLG89PT0hMSYmZT09PSEwJiYoci5jdXJyZW50PWEuc2xpY2UoKSl9LFt0LGEscl0pLEEoKCk9Pnt2YXIgZTtyZXR1cm4oZT1yLmN1cnJlbnQuZmluZChvPT5vIT1udWxsJiZvLmlzQ29ubmVjdGVkKSkhPW51bGw/ZTpudWxsfSl9ZnVuY3Rpb24gWSh7b3duZXJEb2N1bWVudDp0fSxyKXtsZXQgZT1RKHIpO0YoKCk9PntyfHwodD09bnVsbD92b2lkIDA6dC5hY3RpdmVFbGVtZW50KT09PSh0PT1udWxsP3ZvaWQgMDp0LmJvZHkpJiZmKGUoKSl9LFtyXSksRCgoKT0+e3ImJmYoZSgpKX0pfWZ1bmN0aW9uIFooe293bmVyRG9jdW1lbnQ6dCxjb250YWluZXI6cixpbml0aWFsRm9jdXM6ZX0sbyl7bGV0IHU9TChudWxsKSxpPUMoKTtyZXR1cm4gRigoKT0+e2lmKCFvKXJldHVybjtsZXQgbj1yLmN1cnJlbnQ7biYmTygoKT0+e2lmKCFpLmN1cnJlbnQpcmV0dXJuO2xldCBsPXQ9PW51bGw/dm9pZCAwOnQuYWN0aXZlRWxlbWVudDtpZihlIT1udWxsJiZlLmN1cnJlbnQpe2lmKChlPT1udWxsP3ZvaWQgMDplLmN1cnJlbnQpPT09bCl7dS5jdXJyZW50PWw7cmV0dXJufX1lbHNlIGlmKG4uY29udGFpbnMobCkpe3UuY3VycmVudD1sO3JldHVybn1lIT1udWxsJiZlLmN1cnJlbnQ/ZihlLmN1cnJlbnQpOk0obixwLkZpcnN0KT09PXcuRXJyb3ImJmNvbnNvbGUud2FybihcIlRoZXJlIGFyZSBubyBmb2N1c2FibGUgZWxlbWVudHMgaW5zaWRlIHRoZSA8Rm9jdXNUcmFwIC8+XCIpLHUuY3VycmVudD10PT1udWxsP3ZvaWQgMDp0LmFjdGl2ZUVsZW1lbnR9KX0sW29dKSx1fWZ1bmN0aW9uICQoe293bmVyRG9jdW1lbnQ6dCxjb250YWluZXI6cixjb250YWluZXJzOmUscHJldmlvdXNBY3RpdmVFbGVtZW50Om99LHUpe2xldCBpPUMoKTtXKHQ9PW51bGw/dm9pZCAwOnQuZGVmYXVsdFZpZXcsXCJmb2N1c1wiLG49PntpZighdXx8IWkuY3VycmVudClyZXR1cm47bGV0IGw9UChlKTtyLmN1cnJlbnQgaW5zdGFuY2VvZiBIVE1MRWxlbWVudCYmbC5hZGQoci5jdXJyZW50KTtsZXQgbT1vLmN1cnJlbnQ7aWYoIW0pcmV0dXJuO2xldCBjPW4udGFyZ2V0O2MmJmMgaW5zdGFuY2VvZiBIVE1MRWxlbWVudD9TKGwsYyk/KG8uY3VycmVudD1jLGYoYykpOihuLnByZXZlbnREZWZhdWx0KCksbi5zdG9wUHJvcGFnYXRpb24oKSxmKG0pKTpmKG8uY3VycmVudCl9LCEwKX1mdW5jdGlvbiBTKHQscil7Zm9yKGxldCBlIG9mIHQpaWYoZS5jb250YWlucyhyKSlyZXR1cm4hMDtyZXR1cm4hMX1leHBvcnR7Z2UgYXMgRm9jdXNUcmFwfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/components/portal/portal.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/components/portal/portal.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: function() { return /* binding */ pe; },\n/* harmony export */   useNestedPortals: function() { return /* binding */ ae; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _utils_render_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../utils/render.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/render.js\");\n/* harmony import */ var _hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../hooks/use-iso-morphic-effect.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js\");\n/* harmony import */ var _internal_portal_force_root_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../internal/portal-force-root.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/portal-force-root.js\");\n/* harmony import */ var _hooks_use_server_handoff_complete_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../hooks/use-server-handoff-complete.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js\");\n/* harmony import */ var _hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../hooks/use-sync-refs.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-sync-refs.js\");\n/* harmony import */ var _hooks_use_on_unmount_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../hooks/use-on-unmount.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-on-unmount.js\");\n/* harmony import */ var _hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/use-owner.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-owner.js\");\n/* harmony import */ var _utils_env_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/env.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/env.js\");\n/* harmony import */ var _hooks_use_event_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../hooks/use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\nfunction F(p){let l=(0,_internal_portal_force_root_js__WEBPACK_IMPORTED_MODULE_2__.usePortalRoot)(),n=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(v),e=(0,_hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_3__.useOwnerDocument)(p),[a,o]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{if(!l&&n!==null||_utils_env_js__WEBPACK_IMPORTED_MODULE_4__.env.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let r=e.createElement(\"div\");return r.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(r)});return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{a!==null&&(e!=null&&e.body.contains(a)||e==null||e.body.appendChild(a))},[a,e]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{l||n!==null&&o(n.current)},[n,o,l]),a}let U=react__WEBPACK_IMPORTED_MODULE_0__.Fragment;function N(p,l){let n=p,e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),a=(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_5__.useSyncRefs)((0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_5__.optionalRef)(u=>{e.current=u}),l),o=(0,_hooks_use_owner_js__WEBPACK_IMPORTED_MODULE_3__.useOwnerDocument)(e),t=F(e),[r]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{var u;return _utils_env_js__WEBPACK_IMPORTED_MODULE_4__.env.isServer?null:(u=o==null?void 0:o.createElement(\"div\"))!=null?u:null}),i=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(f),C=(0,_hooks_use_server_handoff_complete_js__WEBPACK_IMPORTED_MODULE_6__.useServerHandoffComplete)();return (0,_hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_7__.useIsoMorphicEffect)(()=>{!t||!r||t.contains(r)||(r.setAttribute(\"data-headlessui-portal\",\"\"),t.appendChild(r))},[t,r]),(0,_hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_7__.useIsoMorphicEffect)(()=>{if(r&&i)return i.register(r)},[i,r]),(0,_hooks_use_on_unmount_js__WEBPACK_IMPORTED_MODULE_8__.useOnUnmount)(()=>{var u;!t||!r||(r instanceof Node&&t.contains(r)&&t.removeChild(r),t.childNodes.length<=0&&((u=t.parentElement)==null||u.removeChild(t)))}),C?!t||!r?null:(0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)((0,_utils_render_js__WEBPACK_IMPORTED_MODULE_9__.render)({ourProps:{ref:a},theirProps:n,defaultTag:U,name:\"Portal\"}),r):null}let S=react__WEBPACK_IMPORTED_MODULE_0__.Fragment,v=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);function j(p,l){let{target:n,...e}=p,o={ref:(0,_hooks_use_sync_refs_js__WEBPACK_IMPORTED_MODULE_5__.useSyncRefs)(l)};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(v.Provider,{value:n},(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_9__.render)({ourProps:o,theirProps:e,defaultTag:S,name:\"Popover.Group\"}))}let f=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);function ae(){let p=(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(f),l=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]),n=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_10__.useEvent)(o=>(l.current.push(o),p&&p.register(o),()=>e(o))),e=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_10__.useEvent)(o=>{let t=l.current.indexOf(o);t!==-1&&l.current.splice(t,1),p&&p.unregister(o)}),a=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({register:n,unregister:e,portals:l}),[n,e,l]);return[l,(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>function({children:t}){return react__WEBPACK_IMPORTED_MODULE_0__.createElement(f.Provider,{value:a},t)},[a])]}let D=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_9__.forwardRefWithAs)(N),I=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_9__.forwardRefWithAs)(j),pe=Object.assign(D,{Group:I});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/components/portal/portal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js ***!
  \*************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adjustScrollbarPadding: function() { return /* binding */ c; }\n/* harmony export */ });\nfunction c(){let o;return{before({doc:e}){var l;let n=e.documentElement;o=((l=e.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:e,d:n}){let t=e.documentElement,l=t.clientWidth-t.offsetWidth,r=o-l;n.style(t,\"paddingRight\",`${r}px`)}}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL2RvY3VtZW50LW92ZXJmbG93L2FkanVzdC1zY3JvbGxiYXItcGFkZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsYUFBYSxNQUFNLE9BQU8sUUFBUSxNQUFNLEVBQUUsTUFBTSx3QkFBd0IsOERBQThELFFBQVEsVUFBVSxFQUFFLDREQUE0RCw0QkFBNEIsRUFBRSxPQUEyQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9ob29rcy9kb2N1bWVudC1vdmVyZmxvdy9hZGp1c3Qtc2Nyb2xsYmFyLXBhZGRpbmcuanM/Y2VhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBjKCl7bGV0IG87cmV0dXJue2JlZm9yZSh7ZG9jOmV9KXt2YXIgbDtsZXQgbj1lLmRvY3VtZW50RWxlbWVudDtvPSgobD1lLmRlZmF1bHRWaWV3KSE9bnVsbD9sOndpbmRvdykuaW5uZXJXaWR0aC1uLmNsaWVudFdpZHRofSxhZnRlcih7ZG9jOmUsZDpufSl7bGV0IHQ9ZS5kb2N1bWVudEVsZW1lbnQsbD10LmNsaWVudFdpZHRoLXQub2Zmc2V0V2lkdGgscj1vLWw7bi5zdHlsZSh0LFwicGFkZGluZ1JpZ2h0XCIsYCR7cn1weGApfX19ZXhwb3J0e2MgYXMgYWRqdXN0U2Nyb2xsYmFyUGFkZGluZ307XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleIOSLocking: function() { return /* binding */ T; }\n/* harmony export */ });\n/* harmony import */ var _utils_disposables_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/disposables.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/disposables.js\");\n/* harmony import */ var _utils_platform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/platform.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/platform.js\");\nfunction T(){if(!(0,_utils_platform_js__WEBPACK_IMPORTED_MODULE_0__.isIOS)())return{};let l;return{before(){l=window.pageYOffset},after({doc:o,d:t,meta:s}){function i(n){return s.containers.flatMap(e=>e()).some(e=>e.contains(n))}t.microTask(()=>{if(window.getComputedStyle(o.documentElement).scrollBehavior!==\"auto\"){let e=(0,_utils_disposables_js__WEBPACK_IMPORTED_MODULE_1__.disposables)();e.style(o.documentElement,\"scroll-behavior\",\"auto\"),t.add(()=>t.microTask(()=>e.dispose()))}t.style(o.body,\"marginTop\",`-${l}px`),window.scrollTo(0,0);let n=null;t.addEventListener(o,\"click\",e=>{if(e.target instanceof HTMLElement)try{let r=e.target.closest(\"a\");if(!r)return;let{hash:c}=new URL(r.href),a=o.querySelector(c);a&&!i(a)&&(n=a)}catch{}},!0),t.addEventListener(o,\"touchmove\",e=>{e.target instanceof HTMLElement&&!i(e.target)&&e.preventDefault()},{passive:!1}),t.add(()=>{window.scrollTo(0,window.pageYOffset+l),n&&n.isConnected&&(n.scrollIntoView({block:\"nearest\"}),n=null)})})}}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   overflows: function() { return /* binding */ a; }\n/* harmony export */ });\n/* harmony import */ var _utils_disposables_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/disposables.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/disposables.js\");\n/* harmony import */ var _utils_store_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/store.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/store.js\");\n/* harmony import */ var _adjust_scrollbar_padding_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./adjust-scrollbar-padding.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js\");\n/* harmony import */ var _handle_ios_locking_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./handle-ios-locking.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js\");\n/* harmony import */ var _prevent_scroll_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./prevent-scroll.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js\");\nfunction m(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let a=(0,_utils_store_js__WEBPACK_IMPORTED_MODULE_0__.createStore)(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:(0,_utils_disposables_js__WEBPACK_IMPORTED_MODULE_1__.disposables)(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:m(t)},c=[(0,_handle_ios_locking_js__WEBPACK_IMPORTED_MODULE_2__.handleIOSLocking)(),(0,_adjust_scrollbar_padding_js__WEBPACK_IMPORTED_MODULE_3__.adjustScrollbarPadding)(),(0,_prevent_scroll_js__WEBPACK_IMPORTED_MODULE_4__.preventScroll)()];c.forEach(({before:r})=>r==null?void 0:r(o)),c.forEach(({after:r})=>r==null?void 0:r(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});a.subscribe(()=>{let e=a.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)===\"hidden\",c=t.count!==0;(c&&!o||!c&&o)&&a.dispatch(t.count>0?\"SCROLL_PREVENT\":\"SCROLL_ALLOW\",t),t.count===0&&a.dispatch(\"TEARDOWN\",t)}});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js ***!
  \***************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   preventScroll: function() { return /* binding */ l; }\n/* harmony export */ });\nfunction l(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL2RvY3VtZW50LW92ZXJmbG93L3ByZXZlbnQtc2Nyb2xsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxhQUFhLE9BQU8sUUFBUSxVQUFVLEVBQUUsaURBQTRFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL2RvY3VtZW50LW92ZXJmbG93L3ByZXZlbnQtc2Nyb2xsLmpzP2NiZDYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gbCgpe3JldHVybntiZWZvcmUoe2RvYzplLGQ6b30pe28uc3R5bGUoZS5kb2N1bWVudEVsZW1lbnQsXCJvdmVyZmxvd1wiLFwiaGlkZGVuXCIpfX19ZXhwb3J0e2wgYXMgcHJldmVudFNjcm9sbH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js ***!
  \**********************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocumentOverflowLockedEffect: function() { return /* binding */ p; }\n/* harmony export */ });\n/* harmony import */ var _use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../use-iso-morphic-effect.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js\");\n/* harmony import */ var _hooks_use_store_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../hooks/use-store.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-store.js\");\n/* harmony import */ var _overflow_store_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./overflow-store.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/overflow-store.js\");\nfunction p(e,r,n){let f=(0,_hooks_use_store_js__WEBPACK_IMPORTED_MODULE_0__.useStore)(_overflow_store_js__WEBPACK_IMPORTED_MODULE_1__.overflows),o=e?f.get(e):void 0,i=o?o.count>0:!1;return (0,_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_2__.useIsoMorphicEffect)(()=>{if(!(!e||!r))return _overflow_store_js__WEBPACK_IMPORTED_MODULE_1__.overflows.dispatch(\"PUSH\",e,n),()=>_overflow_store_js__WEBPACK_IMPORTED_MODULE_1__.overflows.dispatch(\"POP\",e,n)},[r,e]),i}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL2RvY3VtZW50LW92ZXJmbG93L3VzZS1kb2N1bWVudC1vdmVyZmxvdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVLLGtCQUFrQixNQUFNLDZEQUFDLENBQUMseURBQUMsdUNBQXVDLE9BQU8sK0VBQUMsTUFBTSxvQkFBb0IseURBQUMsMEJBQTBCLHlEQUFDLHFCQUFxQixVQUF1RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9ob29rcy9kb2N1bWVudC1vdmVyZmxvdy91c2UtZG9jdW1lbnQtb3ZlcmZsb3cuanM/N2RkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7dXNlSXNvTW9ycGhpY0VmZmVjdCBhcyB1fWZyb20nLi4vdXNlLWlzby1tb3JwaGljLWVmZmVjdC5qcyc7aW1wb3J0e3VzZVN0b3JlIGFzIHN9ZnJvbScuLi8uLi9ob29rcy91c2Utc3RvcmUuanMnO2ltcG9ydHtvdmVyZmxvd3MgYXMgdH1mcm9tJy4vb3ZlcmZsb3ctc3RvcmUuanMnO2Z1bmN0aW9uIHAoZSxyLG4pe2xldCBmPXModCksbz1lP2YuZ2V0KGUpOnZvaWQgMCxpPW8/by5jb3VudD4wOiExO3JldHVybiB1KCgpPT57aWYoISghZXx8IXIpKXJldHVybiB0LmRpc3BhdGNoKFwiUFVTSFwiLGUsbiksKCk9PnQuZGlzcGF0Y2goXCJQT1BcIixlLG4pfSxbcixlXSksaX1leHBvcnR7cCBhcyB1c2VEb2N1bWVudE92ZXJmbG93TG9ja2VkRWZmZWN0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event-listener.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-event-listener.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEventListener: function() { return /* binding */ E; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_latest_value_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-latest-value.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-latest-value.js\");\nfunction E(n,e,a,t){let i=(0,_use_latest_value_js__WEBPACK_IMPORTED_MODULE_1__.useLatestValue)(a);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{n=n!=null?n:window;function r(o){i.current(o)}return n.addEventListener(e,r,t),()=>n.removeEventListener(e,r,t)},[n,e,t])}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1ldmVudC1saXN0ZW5lci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUYsb0JBQW9CLE1BQU0sb0VBQUMsSUFBSSxnREFBQyxNQUFNLG1CQUFtQixjQUFjLGFBQWEsa0VBQWtFLFVBQXdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1ldmVudC1saXN0ZW5lci5qcz9lZWRiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2VFZmZlY3QgYXMgZH1mcm9tXCJyZWFjdFwiO2ltcG9ydHt1c2VMYXRlc3RWYWx1ZSBhcyBzfWZyb20nLi91c2UtbGF0ZXN0LXZhbHVlLmpzJztmdW5jdGlvbiBFKG4sZSxhLHQpe2xldCBpPXMoYSk7ZCgoKT0+e249biE9bnVsbD9uOndpbmRvdztmdW5jdGlvbiByKG8pe2kuY3VycmVudChvKX1yZXR1cm4gbi5hZGRFdmVudExpc3RlbmVyKGUscix0KSwoKT0+bi5yZW1vdmVFdmVudExpc3RlbmVyKGUscix0KX0sW24sZSx0XSl9ZXhwb3J0e0UgYXMgdXNlRXZlbnRMaXN0ZW5lcn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event-listener.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-inert.js":
/*!****************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-inert.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInert: function() { return /* binding */ h; }\n/* harmony export */ });\n/* harmony import */ var _use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-iso-morphic-effect.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js\");\nlet u=new Map,t=new Map;function h(r,l=!0){(0,_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_0__.useIsoMorphicEffect)(()=>{var o;if(!l)return;let e=typeof r==\"function\"?r():r.current;if(!e)return;function a(){var d;if(!e)return;let i=(d=t.get(e))!=null?d:1;if(i===1?t.delete(e):t.set(e,i-1),i!==1)return;let n=u.get(e);n&&(n[\"aria-hidden\"]===null?e.removeAttribute(\"aria-hidden\"):e.setAttribute(\"aria-hidden\",n[\"aria-hidden\"]),e.inert=n.inert,u.delete(e))}let f=(o=t.get(e))!=null?o:0;return t.set(e,f+1),f!==0||(u.set(e,{\"aria-hidden\":e.getAttribute(\"aria-hidden\"),inert:e.inert}),e.setAttribute(\"aria-hidden\",\"true\"),e.inert=!0),a},[r,l])}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1pbmVydC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRSx3QkFBd0IsbUJBQW1CLCtFQUFDLE1BQU0sTUFBTSxhQUFhLHlDQUF5QyxhQUFhLGFBQWEsTUFBTSxhQUFhLDZCQUE2QiwrQ0FBK0MsZUFBZSx5SUFBeUksNkJBQTZCLHFDQUFxQywwREFBMEQscURBQXFELFFBQThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1pbmVydC5qcz9lYTYwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2VJc29Nb3JwaGljRWZmZWN0IGFzIHN9ZnJvbScuL3VzZS1pc28tbW9ycGhpYy1lZmZlY3QuanMnO2xldCB1PW5ldyBNYXAsdD1uZXcgTWFwO2Z1bmN0aW9uIGgocixsPSEwKXtzKCgpPT57dmFyIG87aWYoIWwpcmV0dXJuO2xldCBlPXR5cGVvZiByPT1cImZ1bmN0aW9uXCI/cigpOnIuY3VycmVudDtpZighZSlyZXR1cm47ZnVuY3Rpb24gYSgpe3ZhciBkO2lmKCFlKXJldHVybjtsZXQgaT0oZD10LmdldChlKSkhPW51bGw/ZDoxO2lmKGk9PT0xP3QuZGVsZXRlKGUpOnQuc2V0KGUsaS0xKSxpIT09MSlyZXR1cm47bGV0IG49dS5nZXQoZSk7biYmKG5bXCJhcmlhLWhpZGRlblwiXT09PW51bGw/ZS5yZW1vdmVBdHRyaWJ1dGUoXCJhcmlhLWhpZGRlblwiKTplLnNldEF0dHJpYnV0ZShcImFyaWEtaGlkZGVuXCIsbltcImFyaWEtaGlkZGVuXCJdKSxlLmluZXJ0PW4uaW5lcnQsdS5kZWxldGUoZSkpfWxldCBmPShvPXQuZ2V0KGUpKSE9bnVsbD9vOjA7cmV0dXJuIHQuc2V0KGUsZisxKSxmIT09MHx8KHUuc2V0KGUse1wiYXJpYS1oaWRkZW5cIjplLmdldEF0dHJpYnV0ZShcImFyaWEtaGlkZGVuXCIpLGluZXJ0OmUuaW5lcnR9KSxlLnNldEF0dHJpYnV0ZShcImFyaWEtaGlkZGVuXCIsXCJ0cnVlXCIpLGUuaW5lcnQ9ITApLGF9LFtyLGxdKX1leHBvcnR7aCBhcyB1c2VJbmVydH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-inert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-on-unmount.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-on-unmount.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnUnmount: function() { return /* binding */ c; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_micro_task_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/micro-task.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/micro-task.js\");\n/* harmony import */ var _use_event_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\nfunction c(t){let r=(0,_use_event_js__WEBPACK_IMPORTED_MODULE_1__.useEvent)(t),e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(!1);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>(e.current=!1,()=>{e.current=!0,(0,_utils_micro_task_js__WEBPACK_IMPORTED_MODULE_2__.microTask)(()=>{e.current&&r()})}),[r])}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1vbi11bm1vdW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMkksY0FBYyxNQUFNLHVEQUFDLE1BQU0sNkNBQUMsS0FBSyxnREFBQyx3QkFBd0IsYUFBYSwrREFBQyxNQUFNLGVBQWUsRUFBRSxPQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9ob29rcy91c2Utb24tdW5tb3VudC5qcz8yNTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2VSZWYgYXMgdSx1c2VFZmZlY3QgYXMgbn1mcm9tXCJyZWFjdFwiO2ltcG9ydHttaWNyb1Rhc2sgYXMgb31mcm9tJy4uL3V0aWxzL21pY3JvLXRhc2suanMnO2ltcG9ydHt1c2VFdmVudCBhcyBmfWZyb20nLi91c2UtZXZlbnQuanMnO2Z1bmN0aW9uIGModCl7bGV0IHI9Zih0KSxlPXUoITEpO24oKCk9PihlLmN1cnJlbnQ9ITEsKCk9PntlLmN1cnJlbnQ9ITAsbygoKT0+e2UuY3VycmVudCYmcigpfSl9KSxbcl0pfWV4cG9ydHtjIGFzIHVzZU9uVW5tb3VudH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-on-unmount.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-root-containers.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-root-containers.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMainTreeNode: function() { return /* binding */ y; },\n/* harmony export */   useRootContainers: function() { return /* binding */ j; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _internal_hidden_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/hidden.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/hidden.js\");\n/* harmony import */ var _use_event_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\n/* harmony import */ var _use_owner_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-owner.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-owner.js\");\nfunction j({defaultContainers:t=[],portals:r,mainTreeNodeRef:u}={}){var c;let o=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((c=u==null?void 0:u.current)!=null?c:null),l=(0,_use_owner_js__WEBPACK_IMPORTED_MODULE_1__.useOwnerDocument)(o),f=(0,_use_event_js__WEBPACK_IMPORTED_MODULE_2__.useEvent)(()=>{var i;let n=[];for(let e of t)e!==null&&(e instanceof HTMLElement?n.push(e):\"current\"in e&&e.current instanceof HTMLElement&&n.push(e.current));if(r!=null&&r.current)for(let e of r.current)n.push(e);for(let e of(i=l==null?void 0:l.querySelectorAll(\"html > *, body > *\"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!==\"headlessui-portal-root\"&&(e.contains(o.current)||n.some(T=>e.contains(T))||n.push(e));return n});return{resolveContainers:f,contains:(0,_use_event_js__WEBPACK_IMPORTED_MODULE_2__.useEvent)(n=>f().some(i=>i.contains(n))),mainTreeNodeRef:o,MainTreeNode:(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>function(){return u!=null?null:react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_hidden_js__WEBPACK_IMPORTED_MODULE_3__.Hidden,{features:_internal_hidden_js__WEBPACK_IMPORTED_MODULE_3__.Features.Hidden,ref:o})},[o,u])}}function y(){let t=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);return{mainTreeNodeRef:t,MainTreeNode:(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>function(){return react__WEBPACK_IMPORTED_MODULE_0__.createElement(_internal_hidden_js__WEBPACK_IMPORTED_MODULE_3__.Hidden,{features:_internal_hidden_js__WEBPACK_IMPORTED_MODULE_3__.Features.Hidden,ref:t})},[t])}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-root-containers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-store.js":
/*!****************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-store.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStore: function() { return /* binding */ S; }\n/* harmony export */ });\n/* harmony import */ var _use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../use-sync-external-store-shim/index.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/index.js\");\nfunction S(t){return (0,_use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(t.subscribe,t.getSnapshot,t.getSnapshot)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1zdG9yZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRixjQUFjLE9BQU8sNEZBQUMsMENBQWdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS1zdG9yZS5qcz9mZmVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2VTeW5jRXh0ZXJuYWxTdG9yZSBhcyByfWZyb20nLi4vdXNlLXN5bmMtZXh0ZXJuYWwtc3RvcmUtc2hpbS9pbmRleC5qcyc7ZnVuY3Rpb24gUyh0KXtyZXR1cm4gcih0LnN1YnNjcmliZSx0LmdldFNuYXBzaG90LHQuZ2V0U25hcHNob3QpfWV4cG9ydHtTIGFzIHVzZVN0b3JlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-store.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-tab-direction.js":
/*!************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-tab-direction.js ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Direction: function() { return /* binding */ s; },\n/* harmony export */   useTabDirection: function() { return /* binding */ n; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_window_event_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-window-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-window-event.js\");\nvar s=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(s||{});function n(){let e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);return (0,_use_window_event_js__WEBPACK_IMPORTED_MODULE_1__.useWindowEvent)(\"keydown\",o=>{o.key===\"Tab\"&&(e.current=o.shiftKey?1:0)},!0),e}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS10YWItZGlyZWN0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBc0YsNEVBQTRFLEVBQUUsYUFBYSxNQUFNLDZDQUFDLElBQUksT0FBTyxvRUFBQyxlQUFlLDBDQUEwQyxPQUFtRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9ob29rcy91c2UtdGFiLWRpcmVjdGlvbi5qcz8wNzhlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHt1c2VSZWYgYXMgdH1mcm9tXCJyZWFjdFwiO2ltcG9ydHt1c2VXaW5kb3dFdmVudCBhcyBhfWZyb20nLi91c2Utd2luZG93LWV2ZW50LmpzJzt2YXIgcz0ocj0+KHJbci5Gb3J3YXJkcz0wXT1cIkZvcndhcmRzXCIscltyLkJhY2t3YXJkcz0xXT1cIkJhY2t3YXJkc1wiLHIpKShzfHx7fSk7ZnVuY3Rpb24gbigpe2xldCBlPXQoMCk7cmV0dXJuIGEoXCJrZXlkb3duXCIsbz0+e28ua2V5PT09XCJUYWJcIiYmKGUuY3VycmVudD1vLnNoaWZ0S2V5PzE6MCl9LCEwKSxlfWV4cG9ydHtzIGFzIERpcmVjdGlvbixuIGFzIHVzZVRhYkRpcmVjdGlvbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-tab-direction.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-watch.js":
/*!****************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/hooks/use-watch.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWatch: function() { return /* binding */ m; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_event_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\nfunction m(u,t){let e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]),r=(0,_use_event_js__WEBPACK_IMPORTED_MODULE_1__.useEvent)(u);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{let o=[...e.current];for(let[n,a]of t.entries())if(e.current[n]!==a){let l=r(t,o);return e.current=t,l}},[r,...t])}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2hvb2tzL3VzZS13YXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0YsZ0JBQWdCLE1BQU0sNkNBQUMsT0FBTyx1REFBQyxJQUFJLGdEQUFDLE1BQU0scUJBQXFCLGdEQUFnRCxhQUFhLHNCQUFzQixXQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9ob29rcy91c2Utd2F0Y2guanM/ZWVmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7dXNlRWZmZWN0IGFzIHMsdXNlUmVmIGFzIGZ9ZnJvbVwicmVhY3RcIjtpbXBvcnR7dXNlRXZlbnQgYXMgaX1mcm9tJy4vdXNlLWV2ZW50LmpzJztmdW5jdGlvbiBtKHUsdCl7bGV0IGU9ZihbXSkscj1pKHUpO3MoKCk9PntsZXQgbz1bLi4uZS5jdXJyZW50XTtmb3IobGV0W24sYV1vZiB0LmVudHJpZXMoKSlpZihlLmN1cnJlbnRbbl0hPT1hKXtsZXQgbD1yKHQsbyk7cmV0dXJuIGUuY3VycmVudD10LGx9fSxbciwuLi50XSl9ZXhwb3J0e20gYXMgdXNlV2F0Y2h9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-watch.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/hidden.js":
/*!****************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/internal/hidden.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Features: function() { return /* binding */ p; },\n/* harmony export */   Hidden: function() { return /* binding */ c; }\n/* harmony export */ });\n/* harmony import */ var _utils_render_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/render.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/render.js\");\nlet a=\"div\";var p=(e=>(e[e.None=1]=\"None\",e[e.Focusable=2]=\"Focusable\",e[e.Hidden=4]=\"Hidden\",e))(p||{});function s(t,o){let{features:n=1,...e}=t,d={ref:o,\"aria-hidden\":(n&2)===2?!0:void 0,style:{position:\"fixed\",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:\"hidden\",clip:\"rect(0, 0, 0, 0)\",whiteSpace:\"nowrap\",borderWidth:\"0\",...(n&4)===4&&(n&2)!==2&&{display:\"none\"}}};return (0,_utils_render_js__WEBPACK_IMPORTED_MODULE_0__.render)({ourProps:d,theirProps:e,slot:{},defaultTag:a,name:\"Hidden\"})}let c=(0,_utils_render_js__WEBPACK_IMPORTED_MODULE_0__.forwardRefWithAs)(s);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2ludGVybmFsL2hpZGRlbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0UsWUFBWSwyRkFBMkYsRUFBRSxnQkFBZ0IsSUFBSSxrQkFBa0IsTUFBTSwrQ0FBK0MsMktBQTJLLGtCQUFrQixPQUFPLHdEQUFDLEVBQUUsK0JBQStCLDRCQUE0QixFQUFFLE1BQU0sa0VBQUMsSUFBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZWFkbGVzc3VpL3JlYWN0L2Rpc3QvaW50ZXJuYWwvaGlkZGVuLmpzPzI3ZjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2ZvcndhcmRSZWZXaXRoQXMgYXMgcixyZW5kZXIgYXMgaX1mcm9tJy4uL3V0aWxzL3JlbmRlci5qcyc7bGV0IGE9XCJkaXZcIjt2YXIgcD0oZT0+KGVbZS5Ob25lPTFdPVwiTm9uZVwiLGVbZS5Gb2N1c2FibGU9Ml09XCJGb2N1c2FibGVcIixlW2UuSGlkZGVuPTRdPVwiSGlkZGVuXCIsZSkpKHB8fHt9KTtmdW5jdGlvbiBzKHQsbyl7bGV0e2ZlYXR1cmVzOm49MSwuLi5lfT10LGQ9e3JlZjpvLFwiYXJpYS1oaWRkZW5cIjoobiYyKT09PTI/ITA6dm9pZCAwLHN0eWxlOntwb3NpdGlvbjpcImZpeGVkXCIsdG9wOjEsbGVmdDoxLHdpZHRoOjEsaGVpZ2h0OjAscGFkZGluZzowLG1hcmdpbjotMSxvdmVyZmxvdzpcImhpZGRlblwiLGNsaXA6XCJyZWN0KDAsIDAsIDAsIDApXCIsd2hpdGVTcGFjZTpcIm5vd3JhcFwiLGJvcmRlcldpZHRoOlwiMFwiLC4uLihuJjQpPT09NCYmKG4mMikhPT0yJiZ7ZGlzcGxheTpcIm5vbmVcIn19fTtyZXR1cm4gaSh7b3VyUHJvcHM6ZCx0aGVpclByb3BzOmUsc2xvdDp7fSxkZWZhdWx0VGFnOmEsbmFtZTpcIkhpZGRlblwifSl9bGV0IGM9cihzKTtleHBvcnR7cCBhcyBGZWF0dXJlcyxjIGFzIEhpZGRlbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/hidden.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/portal-force-root.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/internal/portal-force-root.js ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ForcePortalRoot: function() { return /* binding */ P; },\n/* harmony export */   usePortalRoot: function() { return /* binding */ l; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nlet e=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(!1);function l(){return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(e)}function P(o){return react__WEBPACK_IMPORTED_MODULE_0__.createElement(e.Provider,{value:o.force},o.children)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2ludGVybmFsL3BvcnRhbC1mb3JjZS1yb290LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5RCxNQUFNLG9EQUFDLEtBQUssYUFBYSxPQUFPLGlEQUFDLElBQUksY0FBYyxPQUFPLGdEQUFlLGFBQWEsY0FBYyxhQUE2RCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC9pbnRlcm5hbC9wb3J0YWwtZm9yY2Utcm9vdC5qcz84NThjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0LHtjcmVhdGVDb250ZXh0IGFzIHIsdXNlQ29udGV4dCBhcyBjfWZyb21cInJlYWN0XCI7bGV0IGU9cighMSk7ZnVuY3Rpb24gbCgpe3JldHVybiBjKGUpfWZ1bmN0aW9uIFAobyl7cmV0dXJuIHQuY3JlYXRlRWxlbWVudChlLlByb3ZpZGVyLHt2YWx1ZTpvLmZvcmNlfSxvLmNoaWxkcmVuKX1leHBvcnR7UCBhcyBGb3JjZVBvcnRhbFJvb3QsbCBhcyB1c2VQb3J0YWxSb290fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/portal-force-root.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/stack-context.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/internal/stack-context.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StackMessage: function() { return /* binding */ s; },\n/* harmony export */   StackProvider: function() { return /* binding */ M; },\n/* harmony export */   useStackContext: function() { return /* binding */ x; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../hooks/use-iso-morphic-effect.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-iso-morphic-effect.js\");\n/* harmony import */ var _hooks_use_event_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../hooks/use-event.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/hooks/use-event.js\");\nlet a=(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(()=>{});a.displayName=\"StackContext\";var s=(e=>(e[e.Add=0]=\"Add\",e[e.Remove=1]=\"Remove\",e))(s||{});function x(){return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(a)}function M({children:i,onUpdate:r,type:e,element:n,enabled:u}){let l=x(),o=(0,_hooks_use_event_js__WEBPACK_IMPORTED_MODULE_1__.useEvent)((...t)=>{r==null||r(...t),l(...t)});return (0,_hooks_use_iso_morphic_effect_js__WEBPACK_IMPORTED_MODULE_2__.useIsoMorphicEffect)(()=>{let t=u===void 0||u===!0;return t&&o(0,e,n),()=>{t&&o(1,e,n)}},[o,e,n,u]),react__WEBPACK_IMPORTED_MODULE_0__.createElement(a.Provider,{value:o},i)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2ludGVybmFsL3N0YWNrLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW1MLE1BQU0sb0RBQUMsT0FBTyxFQUFFLDZCQUE2Qiw0REFBNEQsRUFBRSxhQUFhLE9BQU8saURBQUMsSUFBSSxZQUFZLGlEQUFpRCxFQUFFLFlBQVksNkRBQUMsVUFBVSx5QkFBeUIsRUFBRSxPQUFPLHFGQUFDLE1BQU0seUJBQXlCLHdCQUF3QixhQUFhLFlBQVksZ0RBQWUsYUFBYSxRQUFRLElBQXNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L2ludGVybmFsL3N0YWNrLWNvbnRleHQuanM/NWE3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZCx7Y3JlYXRlQ29udGV4dCBhcyBjLHVzZUNvbnRleHQgYXMgbX1mcm9tXCJyZWFjdFwiO2ltcG9ydHt1c2VJc29Nb3JwaGljRWZmZWN0IGFzIGZ9ZnJvbScuLi9ob29rcy91c2UtaXNvLW1vcnBoaWMtZWZmZWN0LmpzJztpbXBvcnR7dXNlRXZlbnQgYXMgcH1mcm9tJy4uL2hvb2tzL3VzZS1ldmVudC5qcyc7bGV0IGE9YygoKT0+e30pO2EuZGlzcGxheU5hbWU9XCJTdGFja0NvbnRleHRcIjt2YXIgcz0oZT0+KGVbZS5BZGQ9MF09XCJBZGRcIixlW2UuUmVtb3ZlPTFdPVwiUmVtb3ZlXCIsZSkpKHN8fHt9KTtmdW5jdGlvbiB4KCl7cmV0dXJuIG0oYSl9ZnVuY3Rpb24gTSh7Y2hpbGRyZW46aSxvblVwZGF0ZTpyLHR5cGU6ZSxlbGVtZW50Om4sZW5hYmxlZDp1fSl7bGV0IGw9eCgpLG89cCgoLi4udCk9PntyPT1udWxsfHxyKC4uLnQpLGwoLi4udCl9KTtyZXR1cm4gZigoKT0+e2xldCB0PXU9PT12b2lkIDB8fHU9PT0hMDtyZXR1cm4gdCYmbygwLGUsbiksKCk9Pnt0JiZvKDEsZSxuKX19LFtvLGUsbix1XSksZC5jcmVhdGVFbGVtZW50KGEuUHJvdmlkZXIse3ZhbHVlOm99LGkpfWV4cG9ydHtzIGFzIFN0YWNrTWVzc2FnZSxNIGFzIFN0YWNrUHJvdmlkZXIseCBhcyB1c2VTdGFja0NvbnRleHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/internal/stack-context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/use-sync-external-store-shim/index.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSyncExternalStore: function() { return /* binding */ a; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _useSyncExternalStoreShimClient_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useSyncExternalStoreShimClient.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js\");\n/* harmony import */ var _useSyncExternalStoreShimServer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSyncExternalStoreShimServer.js */ \"(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js\");\nconst r=typeof window!=\"undefined\"&&typeof window.document!=\"undefined\"&&typeof window.document.createElement!=\"undefined\",s=!r,c=s?_useSyncExternalStoreShimServer_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore:_useSyncExternalStoreShimClient_js__WEBPACK_IMPORTED_MODULE_2__.useSyncExternalStore,a=\"useSyncExternalStore\" in /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))?(n=>n.useSyncExternalStore)(/*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))):c;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEssb0lBQW9JLG9GQUFDLENBQUMsb0ZBQUMsR0FBRyxtTkFBMEIsNkJBQTZCLHlMQUFDLElBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0vaW5kZXguanM/MmUwOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQqYXMgZSBmcm9tXCJyZWFjdFwiO2ltcG9ydHt1c2VTeW5jRXh0ZXJuYWxTdG9yZSBhcyB0fWZyb20nLi91c2VTeW5jRXh0ZXJuYWxTdG9yZVNoaW1DbGllbnQuanMnO2ltcG9ydHt1c2VTeW5jRXh0ZXJuYWxTdG9yZSBhcyBvfWZyb20nLi91c2VTeW5jRXh0ZXJuYWxTdG9yZVNoaW1TZXJ2ZXIuanMnO2NvbnN0IHI9dHlwZW9mIHdpbmRvdyE9XCJ1bmRlZmluZWRcIiYmdHlwZW9mIHdpbmRvdy5kb2N1bWVudCE9XCJ1bmRlZmluZWRcIiYmdHlwZW9mIHdpbmRvdy5kb2N1bWVudC5jcmVhdGVFbGVtZW50IT1cInVuZGVmaW5lZFwiLHM9IXIsYz1zP286dCxhPVwidXNlU3luY0V4dGVybmFsU3RvcmVcImluIGU/KG49Pm4udXNlU3luY0V4dGVybmFsU3RvcmUpKGUpOmM7ZXhwb3J0e2EgYXMgdXNlU3luY0V4dGVybmFsU3RvcmV9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js ***!
  \************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSyncExternalStore: function() { return /* binding */ y; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction i(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}const d=typeof Object.is==\"function\"?Object.is:i,{useState:u,useEffect:h,useLayoutEffect:f,useDebugValue:p}=/*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)));let S=!1,_=!1;function y(e,t,c){const a=t(),[{inst:n},o]=u({inst:{value:a,getSnapshot:t}});return f(()=>{n.value=a,n.getSnapshot=t,r(n)&&o({inst:n})},[e,a,t]),h(()=>(r(n)&&o({inst:n}),e(()=>{r(n)&&o({inst:n})})),[e]),p(a),a}function r(e){const t=e.getSnapshot,c=e.value;try{const a=t();return!d(c,a)}catch{return!0}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0vdXNlU3luY0V4dGVybmFsU3RvcmVTaGltQ2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF3QixnQkFBZ0IsK0NBQStDLGtEQUFrRCx5REFBeUQsQ0FBQyx5TEFBQyxDQUFDLGNBQWMsa0JBQWtCLGNBQWMsT0FBTyxPQUFPLE1BQU0sdUJBQXVCLEVBQUUsY0FBYyxtQ0FBbUMsT0FBTyxFQUFFLDBCQUEwQixPQUFPLFNBQVMsU0FBUyxPQUFPLEVBQUUsZUFBZSxjQUFjLGdDQUFnQyxJQUFJLFlBQVksY0FBYyxNQUFNLFVBQTRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0vdXNlU3luY0V4dGVybmFsU3RvcmVTaGltQ2xpZW50LmpzP2EyNGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KmFzIGwgZnJvbVwicmVhY3RcIjtmdW5jdGlvbiBpKGUsdCl7cmV0dXJuIGU9PT10JiYoZSE9PTB8fDEvZT09PTEvdCl8fGUhPT1lJiZ0IT09dH1jb25zdCBkPXR5cGVvZiBPYmplY3QuaXM9PVwiZnVuY3Rpb25cIj9PYmplY3QuaXM6aSx7dXNlU3RhdGU6dSx1c2VFZmZlY3Q6aCx1c2VMYXlvdXRFZmZlY3Q6Zix1c2VEZWJ1Z1ZhbHVlOnB9PWw7bGV0IFM9ITEsXz0hMTtmdW5jdGlvbiB5KGUsdCxjKXtjb25zdCBhPXQoKSxbe2luc3Q6bn0sb109dSh7aW5zdDp7dmFsdWU6YSxnZXRTbmFwc2hvdDp0fX0pO3JldHVybiBmKCgpPT57bi52YWx1ZT1hLG4uZ2V0U25hcHNob3Q9dCxyKG4pJiZvKHtpbnN0Om59KX0sW2UsYSx0XSksaCgoKT0+KHIobikmJm8oe2luc3Q6bn0pLGUoKCk9PntyKG4pJiZvKHtpbnN0Om59KX0pKSxbZV0pLHAoYSksYX1mdW5jdGlvbiByKGUpe2NvbnN0IHQ9ZS5nZXRTbmFwc2hvdCxjPWUudmFsdWU7dHJ5e2NvbnN0IGE9dCgpO3JldHVybiFkKGMsYSl9Y2F0Y2h7cmV0dXJuITB9fWV4cG9ydHt5IGFzIHVzZVN5bmNFeHRlcm5hbFN0b3JlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimClient.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js ***!
  \************************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSyncExternalStore: function() { return /* binding */ t; }\n/* harmony export */ });\nfunction t(r,e,n){return e()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3VzZS1zeW5jLWV4dGVybmFsLXN0b3JlLXNoaW0vdXNlU3luY0V4dGVybmFsU3RvcmVTaGltU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxrQkFBa0IsV0FBNkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BoZWFkbGVzc3VpL3JlYWN0L2Rpc3QvdXNlLXN5bmMtZXh0ZXJuYWwtc3RvcmUtc2hpbS91c2VTeW5jRXh0ZXJuYWxTdG9yZVNoaW1TZXJ2ZXIuanM/NjdiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB0KHIsZSxuKXtyZXR1cm4gZSgpfWV4cG9ydHt0IGFzIHVzZVN5bmNFeHRlcm5hbFN0b3JlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/document-ready.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/utils/document-ready.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onDocumentReady: function() { return /* binding */ t; }\n/* harmony export */ });\nfunction t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3V0aWxzL2RvY3VtZW50LXJlYWR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxjQUFjLGFBQWEsMEZBQTBGLGdIQUE2SSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQGhlYWRsZXNzdWkvcmVhY3QvZGlzdC91dGlscy9kb2N1bWVudC1yZWFkeS5qcz8wMTQzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHQobil7ZnVuY3Rpb24gZSgpe2RvY3VtZW50LnJlYWR5U3RhdGUhPT1cImxvYWRpbmdcIiYmKG4oKSxkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwiRE9NQ29udGVudExvYWRlZFwiLGUpKX10eXBlb2Ygd2luZG93IT1cInVuZGVmaW5lZFwiJiZ0eXBlb2YgZG9jdW1lbnQhPVwidW5kZWZpbmVkXCImJihkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKFwiRE9NQ29udGVudExvYWRlZFwiLGUpLGUoKSl9ZXhwb3J0e3QgYXMgb25Eb2N1bWVudFJlYWR5fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/document-ready.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/platform.js":
/*!***************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/utils/platform.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAndroid: function() { return /* binding */ i; },\n/* harmony export */   isIOS: function() { return /* binding */ t; },\n/* harmony export */   isMobile: function() { return /* binding */ n; }\n/* harmony export */ });\nfunction t(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function i(){return/Android/gi.test(window.navigator.userAgent)}function n(){return t()||i()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3V0aWxzL3BsYXRmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLGFBQWEsNkhBQTZILGFBQWEsbURBQW1ELGFBQWEsZ0JBQWdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3V0aWxzL3BsYXRmb3JtLmpzP2RlYTMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gdCgpe3JldHVybi9pUGhvbmUvZ2kudGVzdCh3aW5kb3cubmF2aWdhdG9yLnBsYXRmb3JtKXx8L01hYy9naS50ZXN0KHdpbmRvdy5uYXZpZ2F0b3IucGxhdGZvcm0pJiZ3aW5kb3cubmF2aWdhdG9yLm1heFRvdWNoUG9pbnRzPjB9ZnVuY3Rpb24gaSgpe3JldHVybi9BbmRyb2lkL2dpLnRlc3Qod2luZG93Lm5hdmlnYXRvci51c2VyQWdlbnQpfWZ1bmN0aW9uIG4oKXtyZXR1cm4gdCgpfHxpKCl9ZXhwb3J0e2kgYXMgaXNBbmRyb2lkLHQgYXMgaXNJT1MsbiBhcyBpc01vYmlsZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/platform.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/store.js":
/*!************************************************************!*\
  !*** ./node_modules/@headlessui/react/dist/utils/store.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: function() { return /* binding */ a; }\n/* harmony export */ });\nfunction a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3V0aWxzL3N0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxnQkFBZ0Isb0JBQW9CLE9BQU8sY0FBYyxTQUFTLGNBQWMsZ0NBQWdDLGtCQUFrQix3QkFBd0IsNkJBQXNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVhZGxlc3N1aS9yZWFjdC9kaXN0L3V0aWxzL3N0b3JlLmpzP2U5MzAiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYShvLHIpe2xldCB0PW8oKSxuPW5ldyBTZXQ7cmV0dXJue2dldFNuYXBzaG90KCl7cmV0dXJuIHR9LHN1YnNjcmliZShlKXtyZXR1cm4gbi5hZGQoZSksKCk9Pm4uZGVsZXRlKGUpfSxkaXNwYXRjaChlLC4uLnMpe2xldCBpPXJbZV0uY2FsbCh0LC4uLnMpO2kmJih0PWksbi5mb3JFYWNoKGM9PmMoKSkpfX19ZXhwb3J0e2EgYXMgY3JlYXRlU3RvcmV9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@headlessui/react/dist/utils/store.js\n"));

/***/ })

}]);