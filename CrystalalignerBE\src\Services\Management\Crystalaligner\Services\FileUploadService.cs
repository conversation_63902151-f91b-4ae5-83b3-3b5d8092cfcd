using Crystalaligner.Tools;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Crystalaligner.Services
{
    public interface IFileUploadService
    {
        Task<string> UploadFileAsync(IFormFile file, string category, string? subFolder = null);
        Task<List<string>> UploadMultipleFilesAsync(List<IFormFile> files, string category, string? subFolder = null);
        string GetFileUrl(string relativePath);
        bool DeleteFile(string relativePath);
    }

    public class FileUploadService : IFileUploadService
    {
        private readonly StorageSettings _storageSettings;

        public FileUploadService(IOptions<StorageSettings> storageSettings)
        {
            _storageSettings = storageSettings.Value;
        }

        public async Task<string> UploadFileAsync(IFormFile file, string category, string? subFolder = null)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is null or empty");

            // Get the appropriate path based on category
            var categoryPath = GetCategoryPath(category);
            
            // Create full directory path
            var fullDirectoryPath = Path.Combine(_storageSettings.BasePath, categoryPath);
            if (!string.IsNullOrEmpty(subFolder))
            {
                fullDirectoryPath = Path.Combine(fullDirectoryPath, subFolder);
            }

            // Ensure directory exists
            if (!Directory.Exists(fullDirectoryPath))
                Directory.CreateDirectory(fullDirectoryPath);

            // Generate unique filename
            var fileName = GenerateUniqueFileName(file);
            var fullFilePath = Path.Combine(fullDirectoryPath, fileName);

            // Save file
            using (var stream = new FileStream(fullFilePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // Return relative path for URL generation
            var relativePath = Path.Combine(categoryPath, subFolder ?? "", fileName).Replace("\\", "/");
            return relativePath;
        }

        public async Task<List<string>> UploadMultipleFilesAsync(List<IFormFile> files, string category, string? subFolder = null)
        {
            var uploadedUrls = new List<string>();

            foreach (var file in files)
            {
                if (file != null && file.Length > 0)
                {
                    var url = await UploadFileAsync(file, category, subFolder);
                    uploadedUrls.Add(url);
                }
            }

            return uploadedUrls;
        }

        public string GetFileUrl(string relativePath)
        {
            return $"{_storageSettings.BaseUrl.TrimEnd('/')}/{relativePath.TrimStart('/')}";
        }

        public bool DeleteFile(string relativePath)
        {
            try
            {
                var fullPath = Path.Combine(_storageSettings.BasePath, relativePath);
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private string GetCategoryPath(string category)
        {
            return category.ToLower() switch
            {
                "gallery" => _storageSettings.GalleryPath,
                "products" => _storageSettings.ProductsPath,
                "treatmentplans" => _storageSettings.TreatmentPlansPath,
                "orders" => _storageSettings.OrdersPath,
                "refinements" => _storageSettings.RefinementsPath,
                "treatmentplanattachments" => _storageSettings.TreatmentPlanAttachmentsPath,
                "refinementtreatmentplans" => _storageSettings.RefinementTreatmentPlansPath,
                "refinementtreatmentplanattachments" => _storageSettings.RefinementTreatmentPlanAttachmentsPath,
                "advantages" => _storageSettings.AdvantagesPath,
                "heroes" => _storageSettings.HeroesPath,
                "firstcases" => _storageSettings.FirstCasesPath,
                "testimonials" => _storageSettings.TestimonialsPath,
                "howitworks" => _storageSettings.HowItWorksPath,
                "whoweare" => _storageSettings.WhoWeArePath,
                _ => throw new ArgumentException($"Unknown category: {category}")
            };
        }

        private string GenerateUniqueFileName(IFormFile file)
        {
            var guidPart = Guid.NewGuid().ToString();
            var extension = Path.GetExtension(file.FileName);
            var originalName = Path.GetFileNameWithoutExtension(file.FileName);
            
            // Clean the original name to make it URL-safe
            var cleanName = string.Join("", originalName.Where(c => char.IsLetterOrDigit(c) || c == '-' || c == '_'));
            
            return $"{guidPart}-{cleanName}{extension}";
        }
    }
}
