"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_CrystalApp_HeroBanner_tsx",{

/***/ "(app-pages-browser)/./src/components/CrystalApp/HeroBanner.tsx":
/*!**************************************************!*\
  !*** ./src/components/CrystalApp/HeroBanner.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @styled-icons/material-sharp/ContactSupport */ \"(app-pages-browser)/./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fslightbox-react */ \"(app-pages-browser)/./node_modules/fslightbox-react/index.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fslightbox_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! infinite-zoom-fader */ \"(app-pages-browser)/./node_modules/infinite-zoom-fader/dist/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var styled_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! styled-icons/material */ \"(app-pages-browser)/./node_modules/@styled-icons/material/Quiz/Quiz.esm.js\");\n/* harmony import */ var _QuizModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./QuizModal */ \"(app-pages-browser)/./src/components/CrystalApp/QuizModal.tsx\");\n/* harmony import */ var styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! styled-icons/heroicons-outline */ \"(app-pages-browser)/./node_modules/@styled-icons/heroicons-outline/BookOpen/BookOpen.esm.js\");\n/* harmony import */ var styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! styled-icons/ionicons-outline */ \"(app-pages-browser)/./node_modules/@styled-icons/ionicons-outline/Play/Play.esm.js\");\n/* harmony import */ var _services_SliderService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/SliderService */ \"(app-pages-browser)/./src/services/SliderService.ts\");\n/* harmony import */ var _services_HeroService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/HeroService */ \"(app-pages-browser)/./src/services/HeroService.ts\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HeroBanner = ()=>{\n    _s();\n    // To open the lightbox change the value of the \"toggler\" prop.\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const [toggler, setToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [brochureToggler, setBrochureToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isQuizModalOpen, setIsQuizModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [heroData, setHeroData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [languageId, setLanguageId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const [sliderText, setSliderText] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [imagesLoading, setImagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [sliderData, setSliderData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            setImagesLoading(true);\n            try {\n                const response = await (0,_services_SliderService__WEBPACK_IMPORTED_MODULE_7__.getSlider)();\n                if (response.data && response.data.Data !== sliderData) {\n                    console.log(\"response.data.Data\", response.data.Data);\n                    setSliderData(response.data.Data); // Only update if data has changed\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch slider data:\", error);\n            } finally{\n                setImagesLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const handleLanguageChange = ()=>{\n            const languageCode = i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].language || localStorage.getItem(\"i18nextLng\");\n            setLanguageId(languageCode === \"tr\" ? 1 : 2);\n        };\n        handleLanguageChange();\n        i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].on(\"languageChanged\", handleLanguageChange);\n        return ()=>{\n            i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].off(\"languageChanged\", handleLanguageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        async function fetchData() {\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroSliderText)(languageId);\n                if (response.data) {\n                    setSliderText(response.data.Data);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch FAQs:\", error);\n            }\n        }\n        fetchData();\n    }, [\n        languageId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const getHeroData = async ()=>{\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroBanner)();\n                setHeroData(response.data.Data);\n            } catch (error) {\n                console.error(\"Failed to fetch hero data:\", error);\n            }\n        };\n        getHeroData();\n    }, []);\n    const responseImages = {\n        desktop: sliderData.map((item)=>{\n            return {\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            };\n        }),\n        mobile: sliderData.map((item)=>({\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            }))\n    };\n    /*   const images = {\r\n    desktop: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n      {\r\n        src: sliderData[4]?.Image ? sliderData[4]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[5]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[6]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[7]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[8]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n    ],\r\n    mobile: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n    ],\r\n  }; */ // change title and subtitle with the images, in every 5 seconds\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.title\"));\n    const [subtitle, setSubtitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.subtitle\"));\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            switch(index){\n                case 0:\n                    var _sliderText_, _sliderText_1, _sliderText_2, _sliderText_3;\n                    setTitle(((_sliderText_ = sliderText[0]) === null || _sliderText_ === void 0 ? void 0 : _sliderText_.Title) ? (_sliderText_1 = sliderText[0]) === null || _sliderText_1 === void 0 ? void 0 : _sliderText_1.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_2 = sliderText[0]) === null || _sliderText_2 === void 0 ? void 0 : _sliderText_2.Content) ? (_sliderText_3 = sliderText[0]) === null || _sliderText_3 === void 0 ? void 0 : _sliderText_3.Content : t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n                case 1:\n                    var _sliderText_4, _sliderText_5, _sliderText_6, _sliderText_7;\n                    setTitle(((_sliderText_4 = sliderText[1]) === null || _sliderText_4 === void 0 ? void 0 : _sliderText_4.Title) ? (_sliderText_5 = sliderText[1]) === null || _sliderText_5 === void 0 ? void 0 : _sliderText_5.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_6 = sliderText[1]) === null || _sliderText_6 === void 0 ? void 0 : _sliderText_6.Content) ? (_sliderText_7 = sliderText[1]) === null || _sliderText_7 === void 0 ? void 0 : _sliderText_7.Content : t(\"hero.subtitle2\"));\n                    setIndex(2);\n                    break;\n                case 2:\n                    var _sliderText_8, _sliderText_9, _sliderText_10, _sliderText_11;\n                    setTitle(((_sliderText_8 = sliderText[2]) === null || _sliderText_8 === void 0 ? void 0 : _sliderText_8.Title) ? (_sliderText_9 = sliderText[2]) === null || _sliderText_9 === void 0 ? void 0 : _sliderText_9.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_10 = sliderText[2]) === null || _sliderText_10 === void 0 ? void 0 : _sliderText_10.Content) ? (_sliderText_11 = sliderText[2]) === null || _sliderText_11 === void 0 ? void 0 : _sliderText_11.Content : t(\"hero.subtitle3\"));\n                    setIndex(0);\n                    break;\n                case 3:\n                    var _sliderText_12, _sliderText_13, _sliderText_14, _sliderText_15;\n                    setTitle(((_sliderText_12 = sliderText[3]) === null || _sliderText_12 === void 0 ? void 0 : _sliderText_12.Title) ? (_sliderText_13 = sliderText[3]) === null || _sliderText_13 === void 0 ? void 0 : _sliderText_13.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_14 = sliderText[3]) === null || _sliderText_14 === void 0 ? void 0 : _sliderText_14.Content) ? (_sliderText_15 = sliderText[3]) === null || _sliderText_15 === void 0 ? void 0 : _sliderText_15.Content : t(\"hero.subtitle4\"));\n                    setIndex(0);\n                    break;\n                default:\n                    setTitle(t(\"hero.title\"));\n                    setSubtitle(t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n            }\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, [\n        index,\n        t,\n        sliderText\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[95vh] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuizModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isQuizModalOpen,\n                setIsOpen: setIsQuizModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: toggler,\n                sources: [\n                    heroData === null || heroData === void 0 ? void 0 : heroData.VideoLink\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: brochureToggler,\n                sources: [\n                    heroData === null || heroData === void 0 ? void 0 : heroData.BrochureLink\n                ],\n                type: \"image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            !imagesLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__.InfiniteZoomFader, {\n                images: responseImages,\n                zoom: \"out\",\n                zoomScale: 0.75,\n                zoomTime: 5,\n                zoomMax: 0.25,\n                zoomTimingFunction: \"linear\",\n                transitionTime: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 w-full h-full absolute top-0 left-0 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"home\",\n                        className: \"container max-w-[1760px] xl:px-[30px] h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-[25px] items-center grid-cols-1 md:grid-cols-1 lg:grid-cols-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-[30px] md:space-y-[40px] lg:space-y-[30px] xl:space-y-[40px] lg:max-w-[590px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-h-[200px] md:min-h-[300px] \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \" text-[35px] md:text-[42px] lg:text-[40px] xl:text-[50px] 2xl:text-[55px] leading-[35px] md:leading-[46px] lg:leading-[55px] xl:leading-[64px] mb-[20px] md:mb-[30px] lg:mb-[20px] xl:mb-[40px] text-white\",\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"100\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[16px] md:text-[18px] text-stone-300\",\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"200\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:flex items-center space-y-[20px] sm:space-y-[0] sm:space-x-[30px]\",\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"300\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-row  gap-[30px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"#contact\",\n                                                        className: \"inline-block text-white font-semibold text-[16px] md:text-[18px] transition duration-500 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__.ContactSupport, {\n                                                                size: 48,\n                                                                className: \"mr-[3px] mb-[2px]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            t(\"hero.contact\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                        onClick: ()=>setToggler(!toggler),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-[#fff] w-[36px] h-[36px] leading-[36px] pl-[5px] rounded-full text-center text-[22px] transition duration-500 ease-in-out group-hover:bg-primary-color group-hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__.Play, {\n                                                                    size: 28,\n                                                                    className: \"mr-[3px] mb-[2px]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-[16px] md:text-[18px] text-white\",\n                                                                children: t(\"hero.howItWorks\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                onClick: ()=>setBrochureToggler(!brochureToggler),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-[16px] md:text-[18px] text-sky-300 hover:text-sky-400 md:-ml-2 uppercase underline underline-offset-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__.BookOpen, {\n                                                            size: 20,\n                                                            className: \"mr-[6px] mb-[2px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        t(\"hero.brochure\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsQuizModalOpen(true),\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"400\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        className: \" text-stone-200 font-semibold text-[16px] md:text-[18px] text-left italic underline underline-offset-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_material__WEBPACK_IMPORTED_MODULE_13__.Quiz, {\n                                                size: 28,\n                                                className: \"mr-[2px] inline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            t(\"hero.test\"),\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeroBanner, \"KvSuoIr/C5odsSHWEYLLWsDoVEg=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = HeroBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HeroBanner);\nvar _c;\n$RefreshReg$(_c, \"HeroBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CrystalApp/HeroBanner.tsx\n"));

/***/ })

});