"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_CrystalApp_HeroBanner_tsx",{

/***/ "(app-pages-browser)/./src/components/CrystalApp/HeroBanner.tsx":
/*!**************************************************!*\
  !*** ./src/components/CrystalApp/HeroBanner.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @styled-icons/material-sharp/ContactSupport */ \"(app-pages-browser)/./node_modules/@styled-icons/material-sharp/ContactSupport/ContactSupport.esm.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fslightbox-react */ \"(app-pages-browser)/./node_modules/fslightbox-react/index.js\");\n/* harmony import */ var fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fslightbox_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! infinite-zoom-fader */ \"(app-pages-browser)/./node_modules/infinite-zoom-fader/dist/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var styled_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! styled-icons/material */ \"(app-pages-browser)/./node_modules/@styled-icons/material/Quiz/Quiz.esm.js\");\n/* harmony import */ var _QuizModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./QuizModal */ \"(app-pages-browser)/./src/components/CrystalApp/QuizModal.tsx\");\n/* harmony import */ var styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! styled-icons/heroicons-outline */ \"(app-pages-browser)/./node_modules/@styled-icons/heroicons-outline/BookOpen/BookOpen.esm.js\");\n/* harmony import */ var styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! styled-icons/ionicons-outline */ \"(app-pages-browser)/./node_modules/@styled-icons/ionicons-outline/Play/Play.esm.js\");\n/* harmony import */ var _services_SliderService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/SliderService */ \"(app-pages-browser)/./src/services/SliderService.ts\");\n/* harmony import */ var _services_HeroService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/HeroService */ \"(app-pages-browser)/./src/services/HeroService.ts\");\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst HeroBanner = ()=>{\n    _s();\n    // To open the lightbox change the value of the \"toggler\" prop.\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const [toggler, setToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [brochureToggler, setBrochureToggler] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isQuizModalOpen, setIsQuizModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [heroData, setHeroData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [languageId, setLanguageId] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(1);\n    const [sliderText, setSliderText] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [imagesLoading, setImagesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [sliderData, setSliderData] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            setImagesLoading(true);\n            try {\n                const response = await (0,_services_SliderService__WEBPACK_IMPORTED_MODULE_7__.getSlider)();\n                if (response.data && response.data.Data !== sliderData) {\n                    console.log(\"response.data.Data\", response.data.Data);\n                    setSliderData(response.data.Data); // Only update if data has changed\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch slider data:\", error);\n            } finally{\n                setImagesLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const handleLanguageChange = ()=>{\n            const languageCode = i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].language || localStorage.getItem(\"i18nextLng\");\n            setLanguageId(languageCode === \"tr\" ? 1 : 2);\n        };\n        handleLanguageChange();\n        i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].on(\"languageChanged\", handleLanguageChange);\n        return ()=>{\n            i18next__WEBPACK_IMPORTED_MODULE_9__[\"default\"].off(\"languageChanged\", handleLanguageChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        async function fetchData() {\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroSliderText)(languageId);\n                if (response.data) {\n                    setSliderText(response.data.Data);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch FAQs:\", error);\n            }\n        }\n        fetchData();\n    }, [\n        languageId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const getHeroData = async ()=>{\n            try {\n                const response = await (0,_services_HeroService__WEBPACK_IMPORTED_MODULE_8__.getHeroBanner)();\n                setHeroData(response.data.Data);\n            } catch (error) {\n                console.error(\"Failed to fetch hero data:\", error);\n            }\n        };\n        getHeroData();\n    }, []);\n    const responseImages = {\n        desktop: sliderData.map((item)=>{\n            console.log(\"item.Image\", item.Image);\n            return {\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            };\n        }),\n        mobile: sliderData.map((item)=>({\n                src: item.Image,\n                alt: item.Id.toString() + \" alt\"\n            }))\n    };\n    /*   const images = {\r\n    desktop: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n      {\r\n        src: sliderData[4]?.Image ? sliderData[4]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[5]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[6]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[7]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n      {\r\n        src: sliderData[8]?.Image ? sliderData[5]?.Image : sliderData[0]?.Image,\r\n        alt: \"Image 5 alt\",\r\n      },\r\n    ],\r\n    mobile: [\r\n      {\r\n        src: sliderData[0]?.Image,\r\n        alt: \"Image 1 alt\",\r\n      },\r\n      {\r\n        src: sliderData[1]?.Image,\r\n        alt: \"Image 2 alt\",\r\n      },\r\n      {\r\n        src: sliderData[2]?.Image,\r\n        alt: \"Image 3 alt\",\r\n      },\r\n      {\r\n        src: sliderData[3]?.Image,\r\n        alt: \"Image 4 alt\",\r\n      },\r\n    ],\r\n  }; */ // change title and subtitle with the images, in every 5 seconds\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.title\"));\n    const [subtitle, setSubtitle] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(t(\"hero.subtitle\"));\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            switch(index){\n                case 0:\n                    var _sliderText_, _sliderText_1, _sliderText_2, _sliderText_3;\n                    setTitle(((_sliderText_ = sliderText[0]) === null || _sliderText_ === void 0 ? void 0 : _sliderText_.Title) ? (_sliderText_1 = sliderText[0]) === null || _sliderText_1 === void 0 ? void 0 : _sliderText_1.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_2 = sliderText[0]) === null || _sliderText_2 === void 0 ? void 0 : _sliderText_2.Content) ? (_sliderText_3 = sliderText[0]) === null || _sliderText_3 === void 0 ? void 0 : _sliderText_3.Content : t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n                case 1:\n                    var _sliderText_4, _sliderText_5, _sliderText_6, _sliderText_7;\n                    setTitle(((_sliderText_4 = sliderText[1]) === null || _sliderText_4 === void 0 ? void 0 : _sliderText_4.Title) ? (_sliderText_5 = sliderText[1]) === null || _sliderText_5 === void 0 ? void 0 : _sliderText_5.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_6 = sliderText[1]) === null || _sliderText_6 === void 0 ? void 0 : _sliderText_6.Content) ? (_sliderText_7 = sliderText[1]) === null || _sliderText_7 === void 0 ? void 0 : _sliderText_7.Content : t(\"hero.subtitle2\"));\n                    setIndex(2);\n                    break;\n                case 2:\n                    var _sliderText_8, _sliderText_9, _sliderText_10, _sliderText_11;\n                    setTitle(((_sliderText_8 = sliderText[2]) === null || _sliderText_8 === void 0 ? void 0 : _sliderText_8.Title) ? (_sliderText_9 = sliderText[2]) === null || _sliderText_9 === void 0 ? void 0 : _sliderText_9.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_10 = sliderText[2]) === null || _sliderText_10 === void 0 ? void 0 : _sliderText_10.Content) ? (_sliderText_11 = sliderText[2]) === null || _sliderText_11 === void 0 ? void 0 : _sliderText_11.Content : t(\"hero.subtitle3\"));\n                    setIndex(0);\n                    break;\n                case 3:\n                    var _sliderText_12, _sliderText_13, _sliderText_14, _sliderText_15;\n                    setTitle(((_sliderText_12 = sliderText[3]) === null || _sliderText_12 === void 0 ? void 0 : _sliderText_12.Title) ? (_sliderText_13 = sliderText[3]) === null || _sliderText_13 === void 0 ? void 0 : _sliderText_13.Title : t(\"hero.title\"));\n                    setSubtitle(((_sliderText_14 = sliderText[3]) === null || _sliderText_14 === void 0 ? void 0 : _sliderText_14.Content) ? (_sliderText_15 = sliderText[3]) === null || _sliderText_15 === void 0 ? void 0 : _sliderText_15.Content : t(\"hero.subtitle4\"));\n                    setIndex(0);\n                    break;\n                default:\n                    setTitle(t(\"hero.title\"));\n                    setSubtitle(t(\"hero.subtitle\"));\n                    setIndex(1);\n                    break;\n            }\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, [\n        index,\n        t,\n        sliderText\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[95vh] overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuizModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: isQuizModalOpen,\n                setIsOpen: setIsQuizModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: toggler,\n                sources: [\n                    heroData === null || heroData === void 0 ? void 0 : heroData.VideoLink\n                ]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((fslightbox_react__WEBPACK_IMPORTED_MODULE_1___default()), {\n                toggler: brochureToggler,\n                sources: [\n                    heroData === null || heroData === void 0 ? void 0 : heroData.BrochureLink\n                ],\n                type: \"image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            !imagesLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(infinite_zoom_fader__WEBPACK_IMPORTED_MODULE_2__.InfiniteZoomFader, {\n                images: responseImages,\n                zoom: \"out\",\n                zoomScale: 0.75,\n                zoomTime: 5,\n                zoomMax: 0.25,\n                zoomTimingFunction: \"linear\",\n                transitionTime: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/50 w-full h-full absolute top-0 left-0 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"home\",\n                        className: \"container max-w-[1760px] xl:px-[30px] h-full flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-[25px] items-center grid-cols-1 md:grid-cols-1 lg:grid-cols-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-[30px] md:space-y-[40px] lg:space-y-[30px] xl:space-y-[40px] lg:max-w-[590px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-h-[200px] md:min-h-[300px] \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \" text-[35px] md:text-[42px] lg:text-[40px] xl:text-[50px] 2xl:text-[55px] leading-[35px] md:leading-[46px] lg:leading-[55px] xl:leading-[64px] mb-[20px] md:mb-[30px] lg:mb-[20px] xl:mb-[40px] text-white\",\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"100\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-[16px] md:text-[18px] text-stone-300\",\n                                                \"data-aos\": \"fade-up\",\n                                                \"data-aos-delay\": \"200\",\n                                                \"data-aos-duration\": \"600\",\n                                                \"data-aos-once\": \"true\",\n                                                children: subtitle\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"sm:flex items-center space-y-[20px] sm:space-y-[0] sm:space-x-[30px]\",\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"300\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-row  gap-[30px] \",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"#contact\",\n                                                        className: \"inline-block text-white font-semibold text-[16px] md:text-[18px] transition duration-500 ease-in-out\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_styled_icons_material_sharp_ContactSupport__WEBPACK_IMPORTED_MODULE_10__.ContactSupport, {\n                                                                size: 48,\n                                                                className: \"mr-[3px] mb-[2px]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            t(\"hero.contact\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                        onClick: ()=>setToggler(!toggler),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-[#fff] w-[36px] h-[36px] leading-[36px] pl-[5px] rounded-full text-center text-[22px] transition duration-500 ease-in-out group-hover:bg-primary-color group-hover:text-white\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_ionicons_outline__WEBPACK_IMPORTED_MODULE_11__.Play, {\n                                                                    size: 28,\n                                                                    className: \"mr-[3px] mb-[2px]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold text-[16px] md:text-[18px] text-white\",\n                                                                children: t(\"hero.howItWorks\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-[10px] cursor-pointer group\",\n                                                onClick: ()=>setBrochureToggler(!brochureToggler),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-[16px] md:text-[18px] text-sky-300 hover:text-sky-400 md:-ml-2 uppercase underline underline-offset-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_heroicons_outline__WEBPACK_IMPORTED_MODULE_12__.BookOpen, {\n                                                            size: 20,\n                                                            className: \"mr-[6px] mb-[2px]\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        t(\"hero.brochure\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsQuizModalOpen(true),\n                                        \"data-aos\": \"fade-up\",\n                                        \"data-aos-delay\": \"400\",\n                                        \"data-aos-duration\": \"600\",\n                                        \"data-aos-once\": \"true\",\n                                        className: \" text-stone-200 font-semibold text-[16px] md:text-[18px] text-left italic underline underline-offset-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(styled_icons_material__WEBPACK_IMPORTED_MODULE_13__.Quiz, {\n                                                size: 28,\n                                                className: \"mr-[2px] inline\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            t(\"hero.test\"),\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\web-app\\\\LandingPage\\\\src\\\\components\\\\CrystalApp\\\\HeroBanner.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeroBanner, \"KvSuoIr/C5odsSHWEYLLWsDoVEg=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = HeroBanner;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HeroBanner);\nvar _c;\n$RefreshReg$(_c, \"HeroBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CrystalApp/HeroBanner.tsx\n"));

/***/ })

});