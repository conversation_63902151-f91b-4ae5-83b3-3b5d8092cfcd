// Base Service that will be used to make API calls, it will be extended by other services. Takes base URL and headers as input and returns a promise. It also has a method to handle errors and a method to handle the response. Uses Axios to make API calls. Gets Basic Auth credentials from the environment variables.

// Path: src/services/ApiService.ts

import axios from "axios";

const handleError = (error: any) => {
  return Promise.reject(error);
};

// const NEXT_PUBLIC_APU_URL = "https://api.crystalaligner.com/api/v2/";
const NEXT_PUBLIC_APU_URL = "http://localhost:5000/api/v2/";
const NEXT_PUBLIC_API_USERNAME = "CrystalAligner2023";
const NEXT_PUBLIC_API_PASSWORD = "f98s8Y44sQ7R";

export const GetWithBasic = async (url: string) => {
  try {
    const response = await axios.get(
      NEXT_PUBLIC_APU_URL + url,

      // basic auth
      {
        auth: {
          username: NEXT_PUBLIC_API_USERNAME || "",
          password: NEXT_PUBLIC_API_PASSWORD || "",
        },
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "Access-Control-Allow-Origin": "*",
        },
        withCredentials: true,
      }
    );
    return response;
  } catch (error) {
    return handleError(error);
  }
};
