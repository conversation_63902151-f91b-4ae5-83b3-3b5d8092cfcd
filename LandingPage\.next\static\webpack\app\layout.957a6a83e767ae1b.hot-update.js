/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Mukta\",\"arguments\":[{\"weight\":[\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"],\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"mukta\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Mukta\\\",\\\"arguments\\\":[{\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"mukta\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/remixicon/fonts/remixicon.css */ \"(app-pages-browser)/./node_modules/remixicon/fonts/remixicon.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./public/css/navbar.css */ \"(app-pages-browser)/./public/css/navbar.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./public/fonts/flaticon_mycollection.css */ \"(app-pages-browser)/./public/fonts/flaticon_mycollection.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/swiper/swiper-bundle.css */ \"(app-pages-browser)/./node_modules/swiper/swiper-bundle.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/AosAnimation.tsx */ \"(app-pages-browser)/./src/components/Layout/AosAnimation.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Layout/BackToTop.tsx */ \"(app-pages-browser)/./src/components/Layout/BackToTop.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/TosterProvider.tsx */ \"(app-pages-browser)/./src/providers/TosterProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=false!\n"));

/***/ })

});