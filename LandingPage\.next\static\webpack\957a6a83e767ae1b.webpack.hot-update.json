{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Mukta%22%2C%22arguments%22%3A%5B%7B%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%5D%2C%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22mukta%22%7D&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Ccss%5Cnavbar.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cpublic%5Cfonts%5Cflaticon_mycollection.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cremixicon%5Cfonts%5Cremixicon.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Cnode_modules%5Cswiper%5Cswiper-bundle.css&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CAosAnimation.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Ccomponents%5CLayout%5CBackToTop.tsx&modules=C%3A%5CUsers%5Cburak%5CDesktop%5COrthoClear%5Cweb-app%5CLandingPage%5Csrc%5Cproviders%5CTosterProvider.tsx&server=false!"]}