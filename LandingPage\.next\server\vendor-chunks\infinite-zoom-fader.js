"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/infinite-zoom-fader";
exports.ids = ["vendor-chunks/infinite-zoom-fader"];
exports.modules = {

/***/ "(ssr)/./node_modules/infinite-zoom-fader/dist/esm/index.js":
/*!************************************************************!*\
  !*** ./node_modules/infinite-zoom-fader/dist/esm/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfiniteZoomFader: () => (/* binding */ InfiniteZoomFader)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar propTypes = {exports: {}};\n\nvar reactIs = {exports: {}};\n\nvar reactIs_production_min = {};\n\n/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactIs_production_min;\n\nfunction requireReactIs_production_min () {\n\tif (hasRequiredReactIs_production_min) return reactIs_production_min;\n\thasRequiredReactIs_production_min = 1;\nvar b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\n\tSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\n\tfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}reactIs_production_min.AsyncMode=l;reactIs_production_min.ConcurrentMode=m;reactIs_production_min.ContextConsumer=k;reactIs_production_min.ContextProvider=h;reactIs_production_min.Element=c;reactIs_production_min.ForwardRef=n;reactIs_production_min.Fragment=e;reactIs_production_min.Lazy=t;reactIs_production_min.Memo=r;reactIs_production_min.Portal=d;\n\treactIs_production_min.Profiler=g;reactIs_production_min.StrictMode=f;reactIs_production_min.Suspense=p;reactIs_production_min.isAsyncMode=function(a){return A(a)||z(a)===l};reactIs_production_min.isConcurrentMode=A;reactIs_production_min.isContextConsumer=function(a){return z(a)===k};reactIs_production_min.isContextProvider=function(a){return z(a)===h};reactIs_production_min.isElement=function(a){return \"object\"===typeof a&&null!==a&&a.$$typeof===c};reactIs_production_min.isForwardRef=function(a){return z(a)===n};reactIs_production_min.isFragment=function(a){return z(a)===e};reactIs_production_min.isLazy=function(a){return z(a)===t};\n\treactIs_production_min.isMemo=function(a){return z(a)===r};reactIs_production_min.isPortal=function(a){return z(a)===d};reactIs_production_min.isProfiler=function(a){return z(a)===g};reactIs_production_min.isStrictMode=function(a){return z(a)===f};reactIs_production_min.isSuspense=function(a){return z(a)===p};\n\treactIs_production_min.isValidElementType=function(a){return \"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};reactIs_production_min.typeOf=z;\n\treturn reactIs_production_min;\n}\n\nvar reactIs_development = {};\n\n/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar hasRequiredReactIs_development;\n\nfunction requireReactIs_development () {\n\tif (hasRequiredReactIs_development) return reactIs_development;\n\thasRequiredReactIs_development = 1;\n\n\n\n\tif (true) {\n\t  (function() {\n\n\t// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n\t// nor polyfill, then a plain number is used for performance.\n\tvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\n\tvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\n\tvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\n\tvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\n\tvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\n\tvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\n\tvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\n\tvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n\t// (unstable) APIs that have been removed. Can we remove the symbols?\n\n\tvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\n\tvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\n\tvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\n\tvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\n\tvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\n\tvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\n\tvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\n\tvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\n\tvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\n\tvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\n\tvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\n\tfunction isValidElementType(type) {\n\t  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n\t  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n\t}\n\n\tfunction typeOf(object) {\n\t  if (typeof object === 'object' && object !== null) {\n\t    var $$typeof = object.$$typeof;\n\n\t    switch ($$typeof) {\n\t      case REACT_ELEMENT_TYPE:\n\t        var type = object.type;\n\n\t        switch (type) {\n\t          case REACT_ASYNC_MODE_TYPE:\n\t          case REACT_CONCURRENT_MODE_TYPE:\n\t          case REACT_FRAGMENT_TYPE:\n\t          case REACT_PROFILER_TYPE:\n\t          case REACT_STRICT_MODE_TYPE:\n\t          case REACT_SUSPENSE_TYPE:\n\t            return type;\n\n\t          default:\n\t            var $$typeofType = type && type.$$typeof;\n\n\t            switch ($$typeofType) {\n\t              case REACT_CONTEXT_TYPE:\n\t              case REACT_FORWARD_REF_TYPE:\n\t              case REACT_LAZY_TYPE:\n\t              case REACT_MEMO_TYPE:\n\t              case REACT_PROVIDER_TYPE:\n\t                return $$typeofType;\n\n\t              default:\n\t                return $$typeof;\n\t            }\n\n\t        }\n\n\t      case REACT_PORTAL_TYPE:\n\t        return $$typeof;\n\t    }\n\t  }\n\n\t  return undefined;\n\t} // AsyncMode is deprecated along with isAsyncMode\n\n\tvar AsyncMode = REACT_ASYNC_MODE_TYPE;\n\tvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\n\tvar ContextConsumer = REACT_CONTEXT_TYPE;\n\tvar ContextProvider = REACT_PROVIDER_TYPE;\n\tvar Element = REACT_ELEMENT_TYPE;\n\tvar ForwardRef = REACT_FORWARD_REF_TYPE;\n\tvar Fragment = REACT_FRAGMENT_TYPE;\n\tvar Lazy = REACT_LAZY_TYPE;\n\tvar Memo = REACT_MEMO_TYPE;\n\tvar Portal = REACT_PORTAL_TYPE;\n\tvar Profiler = REACT_PROFILER_TYPE;\n\tvar StrictMode = REACT_STRICT_MODE_TYPE;\n\tvar Suspense = REACT_SUSPENSE_TYPE;\n\tvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\n\tfunction isAsyncMode(object) {\n\t  {\n\t    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n\t      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n\t      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n\t    }\n\t  }\n\n\t  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n\t}\n\tfunction isConcurrentMode(object) {\n\t  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n\t}\n\tfunction isContextConsumer(object) {\n\t  return typeOf(object) === REACT_CONTEXT_TYPE;\n\t}\n\tfunction isContextProvider(object) {\n\t  return typeOf(object) === REACT_PROVIDER_TYPE;\n\t}\n\tfunction isElement(object) {\n\t  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n\t}\n\tfunction isForwardRef(object) {\n\t  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n\t}\n\tfunction isFragment(object) {\n\t  return typeOf(object) === REACT_FRAGMENT_TYPE;\n\t}\n\tfunction isLazy(object) {\n\t  return typeOf(object) === REACT_LAZY_TYPE;\n\t}\n\tfunction isMemo(object) {\n\t  return typeOf(object) === REACT_MEMO_TYPE;\n\t}\n\tfunction isPortal(object) {\n\t  return typeOf(object) === REACT_PORTAL_TYPE;\n\t}\n\tfunction isProfiler(object) {\n\t  return typeOf(object) === REACT_PROFILER_TYPE;\n\t}\n\tfunction isStrictMode(object) {\n\t  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n\t}\n\tfunction isSuspense(object) {\n\t  return typeOf(object) === REACT_SUSPENSE_TYPE;\n\t}\n\n\treactIs_development.AsyncMode = AsyncMode;\n\treactIs_development.ConcurrentMode = ConcurrentMode;\n\treactIs_development.ContextConsumer = ContextConsumer;\n\treactIs_development.ContextProvider = ContextProvider;\n\treactIs_development.Element = Element;\n\treactIs_development.ForwardRef = ForwardRef;\n\treactIs_development.Fragment = Fragment;\n\treactIs_development.Lazy = Lazy;\n\treactIs_development.Memo = Memo;\n\treactIs_development.Portal = Portal;\n\treactIs_development.Profiler = Profiler;\n\treactIs_development.StrictMode = StrictMode;\n\treactIs_development.Suspense = Suspense;\n\treactIs_development.isAsyncMode = isAsyncMode;\n\treactIs_development.isConcurrentMode = isConcurrentMode;\n\treactIs_development.isContextConsumer = isContextConsumer;\n\treactIs_development.isContextProvider = isContextProvider;\n\treactIs_development.isElement = isElement;\n\treactIs_development.isForwardRef = isForwardRef;\n\treactIs_development.isFragment = isFragment;\n\treactIs_development.isLazy = isLazy;\n\treactIs_development.isMemo = isMemo;\n\treactIs_development.isPortal = isPortal;\n\treactIs_development.isProfiler = isProfiler;\n\treactIs_development.isStrictMode = isStrictMode;\n\treactIs_development.isSuspense = isSuspense;\n\treactIs_development.isValidElementType = isValidElementType;\n\treactIs_development.typeOf = typeOf;\n\t  })();\n\t}\n\treturn reactIs_development;\n}\n\nvar hasRequiredReactIs;\n\nfunction requireReactIs () {\n\tif (hasRequiredReactIs) return reactIs.exports;\n\thasRequiredReactIs = 1;\n\n\tif (false) {} else {\n\t  reactIs.exports = requireReactIs_development();\n\t}\n\treturn reactIs.exports;\n}\n\n/*\nobject-assign\n(c) Sindre Sorhus\n@license MIT\n*/\n\nvar objectAssign;\nvar hasRequiredObjectAssign;\n\nfunction requireObjectAssign () {\n\tif (hasRequiredObjectAssign) return objectAssign;\n\thasRequiredObjectAssign = 1;\n\t/* eslint-disable no-unused-vars */\n\tvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\n\tvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\tvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\n\tfunction toObject(val) {\n\t\tif (val === null || val === undefined) {\n\t\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t\t}\n\n\t\treturn Object(val);\n\t}\n\n\tfunction shouldUseNative() {\n\t\ttry {\n\t\t\tif (!Object.assign) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\t\ttest1[5] = 'de';\n\t\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\t\tvar test2 = {};\n\t\t\tfor (var i = 0; i < 10; i++) {\n\t\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t\t}\n\t\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\t\treturn test2[n];\n\t\t\t});\n\t\t\tif (order2.join('') !== '0123456789') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\t\tvar test3 = {};\n\t\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\t\ttest3[letter] = letter;\n\t\t\t});\n\t\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn true;\n\t\t} catch (err) {\n\t\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\t\treturn false;\n\t\t}\n\t}\n\n\tobjectAssign = shouldUseNative() ? Object.assign : function (target, source) {\n\t\tvar from;\n\t\tvar to = toObject(target);\n\t\tvar symbols;\n\n\t\tfor (var s = 1; s < arguments.length; s++) {\n\t\t\tfrom = Object(arguments[s]);\n\n\t\t\tfor (var key in from) {\n\t\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\t\tto[key] = from[key];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (getOwnPropertySymbols) {\n\t\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn to;\n\t};\n\treturn objectAssign;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar ReactPropTypesSecret_1;\nvar hasRequiredReactPropTypesSecret;\n\nfunction requireReactPropTypesSecret () {\n\tif (hasRequiredReactPropTypesSecret) return ReactPropTypesSecret_1;\n\thasRequiredReactPropTypesSecret = 1;\n\n\tvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\n\tReactPropTypesSecret_1 = ReactPropTypesSecret;\n\treturn ReactPropTypesSecret_1;\n}\n\nvar has;\nvar hasRequiredHas;\n\nfunction requireHas () {\n\tif (hasRequiredHas) return has;\n\thasRequiredHas = 1;\n\thas = Function.call.bind(Object.prototype.hasOwnProperty);\n\treturn has;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar checkPropTypes_1;\nvar hasRequiredCheckPropTypes;\n\nfunction requireCheckPropTypes () {\n\tif (hasRequiredCheckPropTypes) return checkPropTypes_1;\n\thasRequiredCheckPropTypes = 1;\n\n\tvar printWarning = function() {};\n\n\tif (true) {\n\t  var ReactPropTypesSecret = requireReactPropTypesSecret();\n\t  var loggedTypeFailures = {};\n\t  var has = requireHas();\n\n\t  printWarning = function(text) {\n\t    var message = 'Warning: ' + text;\n\t    if (typeof console !== 'undefined') {\n\t      console.error(message);\n\t    }\n\t    try {\n\t      // --- Welcome to debugging React ---\n\t      // This error was thrown as a convenience so that you can use this stack\n\t      // to find the callsite that caused this warning to fire.\n\t      throw new Error(message);\n\t    } catch (x) { /**/ }\n\t  };\n\t}\n\n\t/**\n\t * Assert that the values match with the type specs.\n\t * Error messages are memorized and will only be shown once.\n\t *\n\t * @param {object} typeSpecs Map of name to a ReactPropType\n\t * @param {object} values Runtime values that need to be type-checked\n\t * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n\t * @param {string} componentName Name of the component for error messages.\n\t * @param {?Function} getStack Returns the component stack.\n\t * @private\n\t */\n\tfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n\t  if (true) {\n\t    for (var typeSpecName in typeSpecs) {\n\t      if (has(typeSpecs, typeSpecName)) {\n\t        var error;\n\t        // Prop type validation may throw. In case they do, we don't want to\n\t        // fail the render phase where it didn't fail before. So we log it.\n\t        // After these have been cleaned up, we'll let them throw.\n\t        try {\n\t          // This is intentionally an invariant that gets caught. It's the same\n\t          // behavior as without this statement except with a better message.\n\t          if (typeof typeSpecs[typeSpecName] !== 'function') {\n\t            var err = Error(\n\t              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n\t              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n\t              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n\t            );\n\t            err.name = 'Invariant Violation';\n\t            throw err;\n\t          }\n\t          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n\t        } catch (ex) {\n\t          error = ex;\n\t        }\n\t        if (error && !(error instanceof Error)) {\n\t          printWarning(\n\t            (componentName || 'React class') + ': type specification of ' +\n\t            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n\t            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n\t            'You may have forgotten to pass an argument to the type checker ' +\n\t            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n\t            'shape all require an argument).'\n\t          );\n\t        }\n\t        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n\t          // Only monitor this failure once because there tends to be a lot of the\n\t          // same error.\n\t          loggedTypeFailures[error.message] = true;\n\n\t          var stack = getStack ? getStack() : '';\n\n\t          printWarning(\n\t            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n\t          );\n\t        }\n\t      }\n\t    }\n\t  }\n\t}\n\n\t/**\n\t * Resets warning cache when testing.\n\t *\n\t * @private\n\t */\n\tcheckPropTypes.resetWarningCache = function() {\n\t  if (true) {\n\t    loggedTypeFailures = {};\n\t  }\n\t};\n\n\tcheckPropTypes_1 = checkPropTypes;\n\treturn checkPropTypes_1;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar factoryWithTypeCheckers;\nvar hasRequiredFactoryWithTypeCheckers;\n\nfunction requireFactoryWithTypeCheckers () {\n\tif (hasRequiredFactoryWithTypeCheckers) return factoryWithTypeCheckers;\n\thasRequiredFactoryWithTypeCheckers = 1;\n\n\tvar ReactIs = requireReactIs();\n\tvar assign = requireObjectAssign();\n\n\tvar ReactPropTypesSecret = requireReactPropTypesSecret();\n\tvar has = requireHas();\n\tvar checkPropTypes = requireCheckPropTypes();\n\n\tvar printWarning = function() {};\n\n\tif (true) {\n\t  printWarning = function(text) {\n\t    var message = 'Warning: ' + text;\n\t    if (typeof console !== 'undefined') {\n\t      console.error(message);\n\t    }\n\t    try {\n\t      // --- Welcome to debugging React ---\n\t      // This error was thrown as a convenience so that you can use this stack\n\t      // to find the callsite that caused this warning to fire.\n\t      throw new Error(message);\n\t    } catch (x) {}\n\t  };\n\t}\n\n\tfunction emptyFunctionThatReturnsNull() {\n\t  return null;\n\t}\n\n\tfactoryWithTypeCheckers = function(isValidElement, throwOnDirectAccess) {\n\t  /* global Symbol */\n\t  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n\t  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n\t  /**\n\t   * Returns the iterator method function contained on the iterable object.\n\t   *\n\t   * Be sure to invoke the function with the iterable as context:\n\t   *\n\t   *     var iteratorFn = getIteratorFn(myIterable);\n\t   *     if (iteratorFn) {\n\t   *       var iterator = iteratorFn.call(myIterable);\n\t   *       ...\n\t   *     }\n\t   *\n\t   * @param {?object} maybeIterable\n\t   * @return {?function}\n\t   */\n\t  function getIteratorFn(maybeIterable) {\n\t    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n\t    if (typeof iteratorFn === 'function') {\n\t      return iteratorFn;\n\t    }\n\t  }\n\n\t  /**\n\t   * Collection of methods that allow declaration and validation of props that are\n\t   * supplied to React components. Example usage:\n\t   *\n\t   *   var Props = require('ReactPropTypes');\n\t   *   var MyArticle = React.createClass({\n\t   *     propTypes: {\n\t   *       // An optional string prop named \"description\".\n\t   *       description: Props.string,\n\t   *\n\t   *       // A required enum prop named \"category\".\n\t   *       category: Props.oneOf(['News','Photos']).isRequired,\n\t   *\n\t   *       // A prop named \"dialog\" that requires an instance of Dialog.\n\t   *       dialog: Props.instanceOf(Dialog).isRequired\n\t   *     },\n\t   *     render: function() { ... }\n\t   *   });\n\t   *\n\t   * A more formal specification of how these methods are used:\n\t   *\n\t   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n\t   *   decl := ReactPropTypes.{type}(.isRequired)?\n\t   *\n\t   * Each and every declaration produces a function with the same signature. This\n\t   * allows the creation of custom validation functions. For example:\n\t   *\n\t   *  var MyLink = React.createClass({\n\t   *    propTypes: {\n\t   *      // An optional string or URI prop named \"href\".\n\t   *      href: function(props, propName, componentName) {\n\t   *        var propValue = props[propName];\n\t   *        if (propValue != null && typeof propValue !== 'string' &&\n\t   *            !(propValue instanceof URI)) {\n\t   *          return new Error(\n\t   *            'Expected a string or an URI for ' + propName + ' in ' +\n\t   *            componentName\n\t   *          );\n\t   *        }\n\t   *      }\n\t   *    },\n\t   *    render: function() {...}\n\t   *  });\n\t   *\n\t   * @internal\n\t   */\n\n\t  var ANONYMOUS = '<<anonymous>>';\n\n\t  // Important!\n\t  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n\t  var ReactPropTypes = {\n\t    array: createPrimitiveTypeChecker('array'),\n\t    bigint: createPrimitiveTypeChecker('bigint'),\n\t    bool: createPrimitiveTypeChecker('boolean'),\n\t    func: createPrimitiveTypeChecker('function'),\n\t    number: createPrimitiveTypeChecker('number'),\n\t    object: createPrimitiveTypeChecker('object'),\n\t    string: createPrimitiveTypeChecker('string'),\n\t    symbol: createPrimitiveTypeChecker('symbol'),\n\n\t    any: createAnyTypeChecker(),\n\t    arrayOf: createArrayOfTypeChecker,\n\t    element: createElementTypeChecker(),\n\t    elementType: createElementTypeTypeChecker(),\n\t    instanceOf: createInstanceTypeChecker,\n\t    node: createNodeChecker(),\n\t    objectOf: createObjectOfTypeChecker,\n\t    oneOf: createEnumTypeChecker,\n\t    oneOfType: createUnionTypeChecker,\n\t    shape: createShapeTypeChecker,\n\t    exact: createStrictShapeTypeChecker,\n\t  };\n\n\t  /**\n\t   * inlined Object.is polyfill to avoid requiring consumers ship their own\n\t   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n\t   */\n\t  /*eslint-disable no-self-compare*/\n\t  function is(x, y) {\n\t    // SameValue algorithm\n\t    if (x === y) {\n\t      // Steps 1-5, 7-10\n\t      // Steps 6.b-6.e: +0 != -0\n\t      return x !== 0 || 1 / x === 1 / y;\n\t    } else {\n\t      // Step 6.a: NaN == NaN\n\t      return x !== x && y !== y;\n\t    }\n\t  }\n\t  /*eslint-enable no-self-compare*/\n\n\t  /**\n\t   * We use an Error-like object for backward compatibility as people may call\n\t   * PropTypes directly and inspect their output. However, we don't use real\n\t   * Errors anymore. We don't inspect their stack anyway, and creating them\n\t   * is prohibitively expensive if they are created too often, such as what\n\t   * happens in oneOfType() for any type before the one that matched.\n\t   */\n\t  function PropTypeError(message, data) {\n\t    this.message = message;\n\t    this.data = data && typeof data === 'object' ? data: {};\n\t    this.stack = '';\n\t  }\n\t  // Make `instanceof Error` still work for returned errors.\n\t  PropTypeError.prototype = Error.prototype;\n\n\t  function createChainableTypeChecker(validate) {\n\t    if (true) {\n\t      var manualPropTypeCallCache = {};\n\t      var manualPropTypeWarningCount = 0;\n\t    }\n\t    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n\t      componentName = componentName || ANONYMOUS;\n\t      propFullName = propFullName || propName;\n\n\t      if (secret !== ReactPropTypesSecret) {\n\t        if (throwOnDirectAccess) {\n\t          // New behavior only for users of `prop-types` package\n\t          var err = new Error(\n\t            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n\t            'Use `PropTypes.checkPropTypes()` to call them. ' +\n\t            'Read more at http://fb.me/use-check-prop-types'\n\t          );\n\t          err.name = 'Invariant Violation';\n\t          throw err;\n\t        } else if ( true && typeof console !== 'undefined') {\n\t          // Old behavior for people using React.PropTypes\n\t          var cacheKey = componentName + ':' + propName;\n\t          if (\n\t            !manualPropTypeCallCache[cacheKey] &&\n\t            // Avoid spamming the console because they are often not actionable except for lib authors\n\t            manualPropTypeWarningCount < 3\n\t          ) {\n\t            printWarning(\n\t              'You are manually calling a React.PropTypes validation ' +\n\t              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n\t              'and will throw in the standalone `prop-types` package. ' +\n\t              'You may be seeing this warning due to a third-party PropTypes ' +\n\t              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n\t            );\n\t            manualPropTypeCallCache[cacheKey] = true;\n\t            manualPropTypeWarningCount++;\n\t          }\n\t        }\n\t      }\n\t      if (props[propName] == null) {\n\t        if (isRequired) {\n\t          if (props[propName] === null) {\n\t            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n\t          }\n\t          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n\t        }\n\t        return null;\n\t      } else {\n\t        return validate(props, propName, componentName, location, propFullName);\n\t      }\n\t    }\n\n\t    var chainedCheckType = checkType.bind(null, false);\n\t    chainedCheckType.isRequired = checkType.bind(null, true);\n\n\t    return chainedCheckType;\n\t  }\n\n\t  function createPrimitiveTypeChecker(expectedType) {\n\t    function validate(props, propName, componentName, location, propFullName, secret) {\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== expectedType) {\n\t        // `propValue` being instance of, say, date/regexp, pass the 'object'\n\t        // check, but we can offer a more precise error message here rather than\n\t        // 'of type `object`'.\n\t        var preciseType = getPreciseType(propValue);\n\n\t        return new PropTypeError(\n\t          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n\t          {expectedType: expectedType}\n\t        );\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createAnyTypeChecker() {\n\t    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n\t  }\n\n\t  function createArrayOfTypeChecker(typeChecker) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (typeof typeChecker !== 'function') {\n\t        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n\t      }\n\t      var propValue = props[propName];\n\t      if (!Array.isArray(propValue)) {\n\t        var propType = getPropType(propValue);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n\t      }\n\t      for (var i = 0; i < propValue.length; i++) {\n\t        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n\t        if (error instanceof Error) {\n\t          return error;\n\t        }\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createElementTypeChecker() {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      if (!isValidElement(propValue)) {\n\t        var propType = getPropType(propValue);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createElementTypeTypeChecker() {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      if (!ReactIs.isValidElementType(propValue)) {\n\t        var propType = getPropType(propValue);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createInstanceTypeChecker(expectedClass) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (!(props[propName] instanceof expectedClass)) {\n\t        var expectedClassName = expectedClass.name || ANONYMOUS;\n\t        var actualClassName = getClassName(props[propName]);\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createEnumTypeChecker(expectedValues) {\n\t    if (!Array.isArray(expectedValues)) {\n\t      if (true) {\n\t        if (arguments.length > 1) {\n\t          printWarning(\n\t            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n\t            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n\t          );\n\t        } else {\n\t          printWarning('Invalid argument supplied to oneOf, expected an array.');\n\t        }\n\t      }\n\t      return emptyFunctionThatReturnsNull;\n\t    }\n\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      for (var i = 0; i < expectedValues.length; i++) {\n\t        if (is(propValue, expectedValues[i])) {\n\t          return null;\n\t        }\n\t      }\n\n\t      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n\t        var type = getPreciseType(value);\n\t        if (type === 'symbol') {\n\t          return String(value);\n\t        }\n\t        return value;\n\t      });\n\t      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createObjectOfTypeChecker(typeChecker) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (typeof typeChecker !== 'function') {\n\t        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n\t      }\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== 'object') {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n\t      }\n\t      for (var key in propValue) {\n\t        if (has(propValue, key)) {\n\t          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\t          if (error instanceof Error) {\n\t            return error;\n\t          }\n\t        }\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createUnionTypeChecker(arrayOfTypeCheckers) {\n\t    if (!Array.isArray(arrayOfTypeCheckers)) {\n\t       true ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : 0;\n\t      return emptyFunctionThatReturnsNull;\n\t    }\n\n\t    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n\t      var checker = arrayOfTypeCheckers[i];\n\t      if (typeof checker !== 'function') {\n\t        printWarning(\n\t          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n\t          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n\t        );\n\t        return emptyFunctionThatReturnsNull;\n\t      }\n\t    }\n\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var expectedTypes = [];\n\t      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n\t        var checker = arrayOfTypeCheckers[i];\n\t        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n\t        if (checkerResult == null) {\n\t          return null;\n\t        }\n\t        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n\t          expectedTypes.push(checkerResult.data.expectedType);\n\t        }\n\t      }\n\t      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n\t      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createNodeChecker() {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      if (!isNode(props[propName])) {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function invalidValidatorError(componentName, location, propFullName, key, type) {\n\t    return new PropTypeError(\n\t      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n\t      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n\t    );\n\t  }\n\n\t  function createShapeTypeChecker(shapeTypes) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== 'object') {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n\t      }\n\t      for (var key in shapeTypes) {\n\t        var checker = shapeTypes[key];\n\t        if (typeof checker !== 'function') {\n\t          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n\t        }\n\t        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\t        if (error) {\n\t          return error;\n\t        }\n\t      }\n\t      return null;\n\t    }\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function createStrictShapeTypeChecker(shapeTypes) {\n\t    function validate(props, propName, componentName, location, propFullName) {\n\t      var propValue = props[propName];\n\t      var propType = getPropType(propValue);\n\t      if (propType !== 'object') {\n\t        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n\t      }\n\t      // We need to check all keys in case some are required but missing from props.\n\t      var allKeys = assign({}, props[propName], shapeTypes);\n\t      for (var key in allKeys) {\n\t        var checker = shapeTypes[key];\n\t        if (has(shapeTypes, key) && typeof checker !== 'function') {\n\t          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n\t        }\n\t        if (!checker) {\n\t          return new PropTypeError(\n\t            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n\t            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n\t            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n\t          );\n\t        }\n\t        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n\t        if (error) {\n\t          return error;\n\t        }\n\t      }\n\t      return null;\n\t    }\n\n\t    return createChainableTypeChecker(validate);\n\t  }\n\n\t  function isNode(propValue) {\n\t    switch (typeof propValue) {\n\t      case 'number':\n\t      case 'string':\n\t      case 'undefined':\n\t        return true;\n\t      case 'boolean':\n\t        return !propValue;\n\t      case 'object':\n\t        if (Array.isArray(propValue)) {\n\t          return propValue.every(isNode);\n\t        }\n\t        if (propValue === null || isValidElement(propValue)) {\n\t          return true;\n\t        }\n\n\t        var iteratorFn = getIteratorFn(propValue);\n\t        if (iteratorFn) {\n\t          var iterator = iteratorFn.call(propValue);\n\t          var step;\n\t          if (iteratorFn !== propValue.entries) {\n\t            while (!(step = iterator.next()).done) {\n\t              if (!isNode(step.value)) {\n\t                return false;\n\t              }\n\t            }\n\t          } else {\n\t            // Iterator will provide entry [k,v] tuples rather than values.\n\t            while (!(step = iterator.next()).done) {\n\t              var entry = step.value;\n\t              if (entry) {\n\t                if (!isNode(entry[1])) {\n\t                  return false;\n\t                }\n\t              }\n\t            }\n\t          }\n\t        } else {\n\t          return false;\n\t        }\n\n\t        return true;\n\t      default:\n\t        return false;\n\t    }\n\t  }\n\n\t  function isSymbol(propType, propValue) {\n\t    // Native Symbol.\n\t    if (propType === 'symbol') {\n\t      return true;\n\t    }\n\n\t    // falsy value can't be a Symbol\n\t    if (!propValue) {\n\t      return false;\n\t    }\n\n\t    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n\t    if (propValue['@@toStringTag'] === 'Symbol') {\n\t      return true;\n\t    }\n\n\t    // Fallback for non-spec compliant Symbols which are polyfilled.\n\t    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n\t      return true;\n\t    }\n\n\t    return false;\n\t  }\n\n\t  // Equivalent of `typeof` but with special handling for array and regexp.\n\t  function getPropType(propValue) {\n\t    var propType = typeof propValue;\n\t    if (Array.isArray(propValue)) {\n\t      return 'array';\n\t    }\n\t    if (propValue instanceof RegExp) {\n\t      // Old webkits (at least until Android 4.0) return 'function' rather than\n\t      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n\t      // passes PropTypes.object.\n\t      return 'object';\n\t    }\n\t    if (isSymbol(propType, propValue)) {\n\t      return 'symbol';\n\t    }\n\t    return propType;\n\t  }\n\n\t  // This handles more types than `getPropType`. Only used for error messages.\n\t  // See `createPrimitiveTypeChecker`.\n\t  function getPreciseType(propValue) {\n\t    if (typeof propValue === 'undefined' || propValue === null) {\n\t      return '' + propValue;\n\t    }\n\t    var propType = getPropType(propValue);\n\t    if (propType === 'object') {\n\t      if (propValue instanceof Date) {\n\t        return 'date';\n\t      } else if (propValue instanceof RegExp) {\n\t        return 'regexp';\n\t      }\n\t    }\n\t    return propType;\n\t  }\n\n\t  // Returns a string that is postfixed to a warning about an invalid type.\n\t  // For example, \"undefined\" or \"of type array\"\n\t  function getPostfixForTypeWarning(value) {\n\t    var type = getPreciseType(value);\n\t    switch (type) {\n\t      case 'array':\n\t      case 'object':\n\t        return 'an ' + type;\n\t      case 'boolean':\n\t      case 'date':\n\t      case 'regexp':\n\t        return 'a ' + type;\n\t      default:\n\t        return type;\n\t    }\n\t  }\n\n\t  // Returns class name of the object, if any.\n\t  function getClassName(propValue) {\n\t    if (!propValue.constructor || !propValue.constructor.name) {\n\t      return ANONYMOUS;\n\t    }\n\t    return propValue.constructor.name;\n\t  }\n\n\t  ReactPropTypes.checkPropTypes = checkPropTypes;\n\t  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n\t  ReactPropTypes.PropTypes = ReactPropTypes;\n\n\t  return ReactPropTypes;\n\t};\n\treturn factoryWithTypeCheckers;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar factoryWithThrowingShims;\nvar hasRequiredFactoryWithThrowingShims;\n\nfunction requireFactoryWithThrowingShims () {\n\tif (hasRequiredFactoryWithThrowingShims) return factoryWithThrowingShims;\n\thasRequiredFactoryWithThrowingShims = 1;\n\n\tvar ReactPropTypesSecret = requireReactPropTypesSecret();\n\n\tfunction emptyFunction() {}\n\tfunction emptyFunctionWithReset() {}\n\temptyFunctionWithReset.resetWarningCache = emptyFunction;\n\n\tfactoryWithThrowingShims = function() {\n\t  function shim(props, propName, componentName, location, propFullName, secret) {\n\t    if (secret === ReactPropTypesSecret) {\n\t      // It is still safe when called from React.\n\t      return;\n\t    }\n\t    var err = new Error(\n\t      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n\t      'Use PropTypes.checkPropTypes() to call them. ' +\n\t      'Read more at http://fb.me/use-check-prop-types'\n\t    );\n\t    err.name = 'Invariant Violation';\n\t    throw err;\n\t  }\t  shim.isRequired = shim;\n\t  function getShim() {\n\t    return shim;\n\t  }\t  // Important!\n\t  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n\t  var ReactPropTypes = {\n\t    array: shim,\n\t    bigint: shim,\n\t    bool: shim,\n\t    func: shim,\n\t    number: shim,\n\t    object: shim,\n\t    string: shim,\n\t    symbol: shim,\n\n\t    any: shim,\n\t    arrayOf: getShim,\n\t    element: shim,\n\t    elementType: shim,\n\t    instanceOf: getShim,\n\t    node: shim,\n\t    objectOf: getShim,\n\t    oneOf: getShim,\n\t    oneOfType: getShim,\n\t    shape: getShim,\n\t    exact: getShim,\n\n\t    checkPropTypes: emptyFunctionWithReset,\n\t    resetWarningCache: emptyFunction\n\t  };\n\n\t  ReactPropTypes.PropTypes = ReactPropTypes;\n\n\t  return ReactPropTypes;\n\t};\n\treturn factoryWithThrowingShims;\n}\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (true) {\n  var ReactIs = requireReactIs();\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  propTypes.exports = requireFactoryWithTypeCheckers()(ReactIs.isElement, throwOnDirectAccess);\n} else {}\n\nvar propTypesExports = propTypes.exports;\n\nconst MOBILE_WIDTH = 768;\r\nconst DEFAULT_PROPS = {\r\n    zoom: \"out\",\r\n    zoomScale: 0.5,\r\n    zoomTime: 10,\r\n    zoomTimingFunction: \"linear\",\r\n    zoomMax: 0.25,\r\n    transitionTime: 1,\r\n};\n\nconst useScreenSize = () => {\r\n    const [width, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(window.innerWidth);\r\n    const handleWindowSizeChange = () => {\r\n        setWidth(window.innerWidth);\r\n    };\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        window.addEventListener(\"resize\", handleWindowSizeChange);\r\n        return () => {\r\n            window.removeEventListener(\"resize\", handleWindowSizeChange);\r\n        };\r\n    }, []);\r\n    return width <= MOBILE_WIDTH;\r\n};\n\nfunction styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar css_248z = \".izf {\\n  position: relative;\\n  width: 100%;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n.izf__image {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  opacity: 0;\\n  visibility: hidden;\\n}\\n.izf__image--active {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n.izf__image--zoom-out {\\n  transform: scale(var(--izf-scale));\\n}\\n.izf__image--zoom-in {\\n  transform: scale(var(--izf-scale));\\n}\\n.izf__children {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  z-index: calc(var(--izf-max-z-index) + 1);\\n  width: 100%;\\n  height: 100vh;\\n  background: rgba(0, 0, 0, 0.35);\\n  opacity: 0;\\n  overflow: hidden;\\n  animation: izf-children-fade-in 1.25s ease forwards;\\n}\\n\\n@keyframes izf-children-fade-in {\\n  100% {\\n    opacity: 1;\\n  }\\n}\";\nstyleInject(css_248z);\n\nconst InfiniteZoomFader = (props) => {\r\n    var _a;\r\n    const { images, zoom, zoomTime, zoomTimingFunction, transitionTime, children, zoomMax, zoomScale, } = props;\r\n    if (!images)\r\n        return null;\r\n    const isMobile = useScreenSize();\r\n    const imageArray = isMobile ? images === null || images === void 0 ? void 0 : images.mobile : images === null || images === void 0 ? void 0 : images.desktop;\r\n    const [loadedCount, setLoadedCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(2);\r\n    const [previousIndex, setPreviousIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\r\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(-1);\r\n    const [switching, setSwitching] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\r\n    const getVal = (val) => {\r\n        if (val < 0)\r\n            return 0;\r\n        if (val > 1)\r\n            return 1;\r\n        return val;\r\n    };\r\n    const max = getVal(zoomMax);\r\n    const scale = getVal(zoomScale);\r\n    const scaling = zoom === \"in\" ? 1 + max * scale : 1 + max - max * scale;\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        const timeout = setTimeout(() => {\r\n            setActiveIndex(0);\r\n        }, 1);\r\n        return () => clearTimeout(timeout);\r\n    }, []);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        const interval = setInterval(() => {\r\n            setActiveIndex((activeIndex) => activeIndex >= (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1 ? 0 : activeIndex + 1);\r\n            setSwitching(true);\r\n            setLoadedCount((loadedCount) => loadedCount >= (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length)\r\n                ? imageArray === null || imageArray === void 0 ? void 0 : imageArray.length\r\n                : (loadedCount += 1));\r\n            const timeout = setTimeout(() => {\r\n                setSwitching(false);\r\n            }, transitionTime * 1000);\r\n            return () => clearTimeout(timeout);\r\n        }, (zoomTime - transitionTime) * 1000);\r\n        return () => clearInterval(interval);\r\n    }, [zoomTime, transitionTime]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\r\n        setPreviousIndex(activeIndex - 1 < 0 ? (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1 : activeIndex - 1);\r\n    }, [activeIndex]);\r\n    const shouldAnimate = (index) => {\r\n        return (activeIndex === index ||\r\n            (activeIndex === index + 1 && switching) ||\r\n            (activeIndex === 0 &&\r\n                previousIndex === (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1 &&\r\n                switching &&\r\n                (index === 0 || index === (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1)));\r\n    };\r\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: \"izf\", style: {\r\n            [\"--izf-scale\"]: `${zoom === \"out\" ? 1 + getVal(zoomMax) : 1}`,\r\n            [\"--izf-max-z-index\"]: `${imageArray === null || imageArray === void 0 ? void 0 : imageArray.length}`,\r\n        } }, (_a = imageArray === null || imageArray === void 0 ? void 0 : imageArray.slice(0, loadedCount)) === null || _a === void 0 ? void 0 :\r\n        _a.map(({ src, alt }, index) => (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"img\", { className: `izf__image \n            ${shouldAnimate(index) ? \"izf__image--active \" : \"\"}\n            ${zoom === \"out\" && \"izf__image--zoom-out\"}\n            ${zoom === \"in\" && \"izf__image--zoom-in\"} \n          }`, style: shouldAnimate(index)\r\n                ? {\r\n                    transition: `opacity 0.67s ${zoomTimingFunction}, transform ${zoomTime}s ${zoomTimingFunction}`,\r\n                    transform: `scale(${scaling})`,\r\n                    zIndex: `${activeIndex === 0 &&\r\n                        previousIndex === (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - 1\r\n                        ? (imageArray === null || imageArray === void 0 ? void 0 : imageArray.length) - index\r\n                        : \"\"}`,\r\n                }\r\n                : {}, src: src, key: `${src}-${index}`, alt: alt }))),\r\n        children && react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: `izf__children` }, children)));\r\n};\r\nInfiniteZoomFader.defaultProps = DEFAULT_PROPS;\r\nInfiniteZoomFader.propTypes = {\r\n    images: propTypesExports.shape({\r\n        desktop: propTypesExports.arrayOf(propTypesExports.shape({ src: propTypesExports.string, alt: propTypesExports.string })),\r\n        mobile: propTypesExports.arrayOf(propTypesExports.shape({ src: propTypesExports.string, alt: propTypesExports.string })),\r\n    }).isRequired,\r\n    zoom: propTypesExports.oneOf([\"in\", \"out\"]),\r\n    zoomMax: (props) => {\r\n        if (props.zoomScale < 0 || props.zoomScale > 1) {\r\n            return new Error(\"Invalid value supplied as zoomMax: must be a value between 0 and 1\");\r\n        }\r\n    },\r\n    zoomScale: (props) => {\r\n        if (props.zoomScale < 0 || props.zoomScale > 1) {\r\n            return new Error(\"Invalid value supplied as zoomScale: must be a value between 0 and 1\");\r\n        }\r\n    },\r\n    zoomTime: propTypesExports.number,\r\n    zoomTimingFunction: propTypesExports.oneOf([\r\n        \"linear\",\r\n        \"ease\",\r\n        \"ease-in\",\r\n        \"ease-out\",\r\n        \"ease-in-out\",\r\n        RegExp(/cubic-bezier\\\\(([0-9]*\\\\.?[0-9]+),\\\\s*([0-9]*\\\\.?[0-9]+),\\\\s*([0-9]*\\\\.?[0-9]+),\\\\s*([0-9]*\\\\.?[0-9]+)\\\\)/),\r\n    ]),\r\n    transitionTime: propTypesExports.number,\r\n};\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/infinite-zoom-fader/dist/esm/index.js\n");

/***/ })

};
;