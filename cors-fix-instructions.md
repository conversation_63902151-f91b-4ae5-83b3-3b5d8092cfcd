# CORS Sorunu Çözümü

## Sorun

Frontend (https://www.crystalaligner.com) backend'e (https://api.crystalaligner.com) istek atarken CORS hatası alıyordu.

## Ya<PERSON><PERSON><PERSON>şiklikler

### 1. Backend CORS Ayarları (Startup.cs)

- `www.crystalaligner.com` domain'i CORS izin listesine eklendi
- `localhost:3005` (Next.js development port) eklendi
- CORS middleware eklendi

### 2. Web.config Düzeltmeleri

- Publish output'taki `web.config` dosyasında CORS header'ları aktif hale getirildi
- Ana `web.config` dosyasında CORS header'ları zaten aktifti

### 3. CORS Middleware Eklendi

- `CorsMiddleware.cs` dosyası oluşturuldu
- OPTIONS request'leri için özel handling eklendi
- Tüm CORS header'ları otomatik olarak ekleniyor

### 4. Frontend API Service Güncellendi

- API çağrılarına CORS header'ları eklendi
- `withCredentials: true` eklendi

## Deploy Adımları

### Backend Deploy

1. `CrystalalignerBE/deploy-cors-fix.bat` dosyasını çalıştırın
2. Veya manuel olarak:
   ```bash
   cd CrystalalignerBE/src/Services/Management/Crystalaligner
   dotnet build -c Release
   dotnet publish -c Release -o ../../../publish_output
   ```
3. Backend'i yeniden başlatın

### Frontend Deploy

1. LandingPage klasöründe:
   ```bash
   npm run build
   npm run start
   ```

## Test Etme

1. Browser'da Developer Tools'u açın
2. Network sekmesine gidin
3. https://www.crystalaligner.com adresine gidin
4. API çağrılarının başarılı olduğunu kontrol edin

## Olası Sorunlar ve Çözümler

### Hala CORS Hatası Alıyorsanız:

1. Backend'in yeniden başlatıldığından emin olun
2. Browser cache'ini temizleyin
3. CDN cache'ini temizleyin (varsa)
4. IIS/nginx ayarlarını kontrol edin

### Production'da Sorun Yaşıyorsanız:

1. Load balancer ayarlarını kontrol edin
2. Reverse proxy ayarlarını kontrol edin
3. SSL sertifika ayarlarını kontrol edin

## Ek Güvenlik Notları

- CORS ayarlarında sadece gerekli domain'leri listeleyin
- `Access-Control-Allow-Origin: *` yerine spesifik domain'ler kullanın
- Production'da `AllowCredentials: true` kullanırken dikkatli olun
