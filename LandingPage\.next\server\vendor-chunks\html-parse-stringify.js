/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/html-parse-stringify";
exports.ids = ["vendor-chunks/html-parse-stringify"];
exports.modules = {

/***/ "(ssr)/./node_modules/html-parse-stringify/dist/html-parse-stringify.js":
/*!************************************************************************!*\
  !*** ./node_modules/html-parse-stringify/dist/html-parse-stringify.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var e,t=(e=__webpack_require__(/*! void-elements */ \"(ssr)/./node_modules/void-elements/index.js\"))&&\"object\"==typeof e&&\"default\"in e?e.default:e,n=/\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g;function r(e){var r={type:\"tag\",name:\"\",voidElement:!1,attrs:{},children:[]},i=e.match(/<\\/?([^\\s]+?)[/\\s>]/);if(i&&(r.name=i[1],(t[i[1]]||\"/\"===e.charAt(e.length-2))&&(r.voidElement=!0),r.name.startsWith(\"!--\"))){var s=e.indexOf(\"--\\x3e\");return{type:\"comment\",comment:-1!==s?e.slice(4,s):\"\"}}for(var c=new RegExp(n),a=null;null!==(a=c.exec(e));)if(a[0].trim())if(a[1]){var o=a[1].trim(),u=[o,\"\"];o.indexOf(\"=\")>-1&&(u=o.split(\"=\")),r.attrs[u[0]]=u[1],c.lastIndex--}else a[2]&&(r.attrs[a[2]]=a[3].trim().substring(1,a[3].length-1));return r}var i=/<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g,s=/^\\s*$/,c=Object.create(null);function a(e,t){switch(t.type){case\"text\":return e+t.content;case\"tag\":return e+=\"<\"+t.name+(t.attrs?function(e){var t=[];for(var n in e)t.push(n+'=\"'+e[n]+'\"');return t.length?\" \"+t.join(\" \"):\"\"}(t.attrs):\"\")+(t.voidElement?\"/>\":\">\"),t.voidElement?e:e+t.children.reduce(a,\"\")+\"</\"+t.name+\">\";case\"comment\":return e+\"\\x3c!--\"+t.comment+\"--\\x3e\"}}module.exports={parse:function(e,t){t||(t={}),t.components||(t.components=c);var n,a=[],o=[],u=-1,l=!1;if(0!==e.indexOf(\"<\")){var m=e.indexOf(\"<\");a.push({type:\"text\",content:-1===m?e:e.substring(0,m)})}return e.replace(i,function(i,c){if(l){if(i!==\"</\"+n.name+\">\")return;l=!1}var m,d=\"/\"!==i.charAt(1),f=i.startsWith(\"\\x3c!--\"),h=c+i.length,p=e.charAt(h);if(f){var v=r(i);return u<0?(a.push(v),a):((m=o[u]).children.push(v),a)}if(d&&(u++,\"tag\"===(n=r(i)).type&&t.components[n.name]&&(n.type=\"component\",l=!0),n.voidElement||l||!p||\"<\"===p||n.children.push({type:\"text\",content:e.slice(h,e.indexOf(\"<\",h))}),0===u&&a.push(n),(m=o[u-1])&&m.children.push(n),o[u]=n),(!d||n.voidElement)&&(u>-1&&(n.voidElement||n.name===i.slice(2,-1))&&(u--,n=-1===u?a:o[u]),!l&&\"<\"!==p&&p)){m=-1===u?a:o[u].children;var x=e.indexOf(\"<\",h),g=e.slice(h,-1===x?void 0:x);s.test(g)&&(g=\" \"),(x>-1&&u+m.length>=0||\" \"!==g)&&m.push({type:\"text\",content:g})}}),a},stringify:function(e){return e.reduce(function(e,t){return e+a(\"\",t)},\"\")}};\n//# sourceMappingURL=html-parse-stringify.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaHRtbC1wYXJzZS1zdHJpbmdpZnkvZGlzdC9odG1sLXBhcnNlLXN0cmluZ2lmeS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxXQUFXLG1CQUFPLENBQUMsa0VBQWUseUdBQXlHLGNBQWMsT0FBTywwQ0FBMEMsYUFBYSxrQ0FBa0Msd0dBQXdHLDBCQUEwQixPQUFPLCtDQUErQywrQkFBK0IscUJBQXFCLHlCQUF5QiwyQkFBMkIscUVBQXFFLGtFQUFrRSxTQUFTLHdGQUF3RixnQkFBZ0IsZUFBZSw4QkFBOEIsb0RBQW9ELFNBQVMsdUNBQXVDLG1DQUFtQyxpR0FBaUcscURBQXFELGdCQUFnQixvQkFBb0IsUUFBUSxpQ0FBaUMsMEJBQTBCLHVCQUF1QixxQkFBcUIsUUFBUSw4Q0FBOEMsRUFBRSxpQ0FBaUMsTUFBTSw4QkFBOEIsS0FBSywrRUFBK0UsTUFBTSxXQUFXLHVEQUF1RCxrSUFBa0ksZ0RBQWdELHNLQUFzSyx5QkFBeUIsb0RBQW9ELDJEQUEyRCxzQkFBc0IsR0FBRyxJQUFJLHVCQUF1Qiw4QkFBOEIsaUJBQWlCO0FBQzFrRSIsInNvdXJjZXMiOlsid2VicGFjazovL2NyeXN0YWxhbGlnbmVyLy4vbm9kZV9tb2R1bGVzL2h0bWwtcGFyc2Utc3RyaW5naWZ5L2Rpc3QvaHRtbC1wYXJzZS1zdHJpbmdpZnkuanM/NzUxZiJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZSx0PShlPXJlcXVpcmUoXCJ2b2lkLWVsZW1lbnRzXCIpKSYmXCJvYmplY3RcIj09dHlwZW9mIGUmJlwiZGVmYXVsdFwiaW4gZT9lLmRlZmF1bHQ6ZSxuPS9cXHMoW14nXCIvXFxzPjxdKz8pW1xccy8+XXwoW15cXHM9XSspPVxccz8oXCIuKj9cInwnLio/JykvZztmdW5jdGlvbiByKGUpe3ZhciByPXt0eXBlOlwidGFnXCIsbmFtZTpcIlwiLHZvaWRFbGVtZW50OiExLGF0dHJzOnt9LGNoaWxkcmVuOltdfSxpPWUubWF0Y2goLzxcXC8/KFteXFxzXSs/KVsvXFxzPl0vKTtpZihpJiYoci5uYW1lPWlbMV0sKHRbaVsxXV18fFwiL1wiPT09ZS5jaGFyQXQoZS5sZW5ndGgtMikpJiYoci52b2lkRWxlbWVudD0hMCksci5uYW1lLnN0YXJ0c1dpdGgoXCIhLS1cIikpKXt2YXIgcz1lLmluZGV4T2YoXCItLVxceDNlXCIpO3JldHVybnt0eXBlOlwiY29tbWVudFwiLGNvbW1lbnQ6LTEhPT1zP2Uuc2xpY2UoNCxzKTpcIlwifX1mb3IodmFyIGM9bmV3IFJlZ0V4cChuKSxhPW51bGw7bnVsbCE9PShhPWMuZXhlYyhlKSk7KWlmKGFbMF0udHJpbSgpKWlmKGFbMV0pe3ZhciBvPWFbMV0udHJpbSgpLHU9W28sXCJcIl07by5pbmRleE9mKFwiPVwiKT4tMSYmKHU9by5zcGxpdChcIj1cIikpLHIuYXR0cnNbdVswXV09dVsxXSxjLmxhc3RJbmRleC0tfWVsc2UgYVsyXSYmKHIuYXR0cnNbYVsyXV09YVszXS50cmltKCkuc3Vic3RyaW5nKDEsYVszXS5sZW5ndGgtMSkpO3JldHVybiByfXZhciBpPS88W2EtekEtWjAtOVxcLVxcIVxcL10oPzpcIlteXCJdKlwifCdbXiddKid8W14nXCI+XSkqPi9nLHM9L15cXHMqJC8sYz1PYmplY3QuY3JlYXRlKG51bGwpO2Z1bmN0aW9uIGEoZSx0KXtzd2l0Y2godC50eXBlKXtjYXNlXCJ0ZXh0XCI6cmV0dXJuIGUrdC5jb250ZW50O2Nhc2VcInRhZ1wiOnJldHVybiBlKz1cIjxcIit0Lm5hbWUrKHQuYXR0cnM/ZnVuY3Rpb24oZSl7dmFyIHQ9W107Zm9yKHZhciBuIGluIGUpdC5wdXNoKG4rJz1cIicrZVtuXSsnXCInKTtyZXR1cm4gdC5sZW5ndGg/XCIgXCIrdC5qb2luKFwiIFwiKTpcIlwifSh0LmF0dHJzKTpcIlwiKSsodC52b2lkRWxlbWVudD9cIi8+XCI6XCI+XCIpLHQudm9pZEVsZW1lbnQ/ZTplK3QuY2hpbGRyZW4ucmVkdWNlKGEsXCJcIikrXCI8L1wiK3QubmFtZStcIj5cIjtjYXNlXCJjb21tZW50XCI6cmV0dXJuIGUrXCJcXHgzYyEtLVwiK3QuY29tbWVudCtcIi0tXFx4M2VcIn19bW9kdWxlLmV4cG9ydHM9e3BhcnNlOmZ1bmN0aW9uKGUsdCl7dHx8KHQ9e30pLHQuY29tcG9uZW50c3x8KHQuY29tcG9uZW50cz1jKTt2YXIgbixhPVtdLG89W10sdT0tMSxsPSExO2lmKDAhPT1lLmluZGV4T2YoXCI8XCIpKXt2YXIgbT1lLmluZGV4T2YoXCI8XCIpO2EucHVzaCh7dHlwZTpcInRleHRcIixjb250ZW50Oi0xPT09bT9lOmUuc3Vic3RyaW5nKDAsbSl9KX1yZXR1cm4gZS5yZXBsYWNlKGksZnVuY3Rpb24oaSxjKXtpZihsKXtpZihpIT09XCI8L1wiK24ubmFtZStcIj5cIilyZXR1cm47bD0hMX12YXIgbSxkPVwiL1wiIT09aS5jaGFyQXQoMSksZj1pLnN0YXJ0c1dpdGgoXCJcXHgzYyEtLVwiKSxoPWMraS5sZW5ndGgscD1lLmNoYXJBdChoKTtpZihmKXt2YXIgdj1yKGkpO3JldHVybiB1PDA/KGEucHVzaCh2KSxhKTooKG09b1t1XSkuY2hpbGRyZW4ucHVzaCh2KSxhKX1pZihkJiYodSsrLFwidGFnXCI9PT0obj1yKGkpKS50eXBlJiZ0LmNvbXBvbmVudHNbbi5uYW1lXSYmKG4udHlwZT1cImNvbXBvbmVudFwiLGw9ITApLG4udm9pZEVsZW1lbnR8fGx8fCFwfHxcIjxcIj09PXB8fG4uY2hpbGRyZW4ucHVzaCh7dHlwZTpcInRleHRcIixjb250ZW50OmUuc2xpY2UoaCxlLmluZGV4T2YoXCI8XCIsaCkpfSksMD09PXUmJmEucHVzaChuKSwobT1vW3UtMV0pJiZtLmNoaWxkcmVuLnB1c2gobiksb1t1XT1uKSwoIWR8fG4udm9pZEVsZW1lbnQpJiYodT4tMSYmKG4udm9pZEVsZW1lbnR8fG4ubmFtZT09PWkuc2xpY2UoMiwtMSkpJiYodS0tLG49LTE9PT11P2E6b1t1XSksIWwmJlwiPFwiIT09cCYmcCkpe209LTE9PT11P2E6b1t1XS5jaGlsZHJlbjt2YXIgeD1lLmluZGV4T2YoXCI8XCIsaCksZz1lLnNsaWNlKGgsLTE9PT14P3ZvaWQgMDp4KTtzLnRlc3QoZykmJihnPVwiIFwiKSwoeD4tMSYmdSttLmxlbmd0aD49MHx8XCIgXCIhPT1nKSYmbS5wdXNoKHt0eXBlOlwidGV4dFwiLGNvbnRlbnQ6Z30pfX0pLGF9LHN0cmluZ2lmeTpmdW5jdGlvbihlKXtyZXR1cm4gZS5yZWR1Y2UoZnVuY3Rpb24oZSx0KXtyZXR1cm4gZSthKFwiXCIsdCl9LFwiXCIpfX07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1odG1sLXBhcnNlLXN0cmluZ2lmeS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/html-parse-stringify/dist/html-parse-stringify.js\n");

/***/ })

};
;